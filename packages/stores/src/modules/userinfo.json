{"account": "FAQC", "realName": "富奥汽车", "avatar": null, "introduction": null, "address": null, "signature": null, "orgId": **************, "orgName": "富奥汽车", "orgType": "201", "posName": null, "buttons": ["sysUser:page", "sysUser:update", "sysUser:add", "sysUser:delete", "sysUser:detail", "sysUser:grantRole", "sysUser:resetPwd", "sysUser:setStatus", "sysOnlineUser:forceOffline", "sysRole:page", "sysRole:update", "sysRole:add", "sysRole:delete", "sysRole:grantMenu", "sysRole:grantDataScope", "sysRole:setStatus", "sysOrg:update", "sysOrg:add", "sysOrg:delete", "sysPos:list", "sysPos:update", "sysPos:add", "sysPos:delete", "sysUser:changePwd", "sysUser:baseInfo", "sysFile:uploadSignature", "sysFile:uploadAvatar", "sysNotice:page", "sysNotice:update", "sysNotice:add", "sysNotice:delete", "sysNotice:public", "sysNotice:cancel", "sysMenu:list", "sysMenu:update", "sysMenu:add", "sysMenu:delete", "sysConfig:page", "sysConfig:update", "sysConfig:add", "sysConfig:delete", "sysDictType:page", "sysDictType:update", "sysDictType:add", "sysDictType:delete", "sysJob:pageJobDetail", "sysJob:updateJobDetail", "sysJob:addJobDetail", "sysJob:deleteJobDetail", "sysCache:keyList", "sysCache:delete", "sysRegion:page", "sysRegion:update", "sysRegion:add", "sysRegion:delete", "sysRegion:sync", "sysFile:page", "sysFile:uploadFile", "sysFile:downloadFile", "sysFile:delete", "sysVislog:page", "sysVislog:clear", "sysOplog:page", "sysOplog:clear", "sysOplog:export", "sysExlog:page", "sysExlog:clear", "sysExlog:export", "sysDifflog:page", "sysDifflog:clear", "dev:page", "dev:detail", "dev:add", "dev:delete", "dev:edit", "dev:DevTypeDevTypeIDDropdown", "dev:<PERSON><PERSON><PERSON>", "iotDev:page", "iotDev:detail", "iotDev:add", "iotDev:delete", "iotDev:edit", "devType:page", "devType:detail", "devType:add", "devType:delete", "devType:edit", "devTypeArr:page", "devTypeArr:detail", "devTypeArr:add", "devTypeArr:delete", "devTypeArr:edit", "devTypeArr:DevTypeDevTypeIDDropdown", "devIO:page", "devIO:detail", "devIO:add", "devIO:delete", "devIO:edit", "devIO:DevTree", "devAttr:page", "devAttr:detail", "devAttr:add", "devAttr:delete", "devAttr:edit", "devAttr:DevTypeArrAttrIDDropdown", "devAttr:DevTree", "iotAndDev:page", "iotAndDev:detail", "iotAndDev:add", "iotAndDev:delete", "iotAndDev:edit", "iotAndDev:IotDevIotDevIdDropdown", "iotAndDev:DevTreeOutputDevIdDropdown", "readPlan:page", "readPlan:detail", "readPlan:add", "readPlan:delete", "readPlan:edit", "readPlan:DevTypeDevTypeIdDropdown", "readPlanItem:page", "readPlanItem:detail", "readPlanItem:add", "readPlanItem:delete", "readPlanItem:edit", "readPlanItem:ReadPlanReadPlanIdDropdown", "readPlanItem:DevTypeArrAttrIDDropdown", "userMenu:page", "userMenu:detail", "userMenu:add", "userMenu:delete", "userMenu:edit", "userMenu:UserMenuTree"]}