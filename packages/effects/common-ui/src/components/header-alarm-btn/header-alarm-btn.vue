<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { ElButton } from 'element-plus';
// import { getCountOfAllStatus } from '@/api/system/alarm'

// 响应式数据
const general = ref(0);
const emergent = ref(0);
const serious = ref(0);

// 路由实例
const router = useRouter();

// 跳转到告警详情页面
function toAlarmDetail(level: number) {
  router.push({ path: '/alarm/realtime-alarm', query: { alarmLevel: level } });
}

// 获取告警数量
function getCount() {
  // getCountOfAllStatus().then(res => {
  //   res.data.forEach(item => {
  //     if (item.alarmLevel === "0") {
  //       general.value = item.count;
  //     } else if (item.alarmLevel === "1") {
  //       emergent.value = item.count;
  //     } else if (item.alarmLevel === "2") {
  //       serious.value = item.count;
  //     }
  //   })
  // })
}

// 组件初始化时调用
getCount();
</script>

<template>
  <div class="btn-container flex-between">
    <div class="alarm-item mr-5">
      <ElButton type="success" size="small" @click="toAlarmDetail(0)">
        一般
      </ElButton>
      <sup class="alarm-num-success">{{ general > 99 ? '99+' : general }}</sup>
    </div>
    <div class="alarm-item mr-5">
      <ElButton type="warning" size="small" @click="toAlarmDetail(1)">
        紧急
      </ElButton>
      <sup class="alarm-num-warning">{{
        emergent > 99 ? '99+' : emergent
      }}</sup>
    </div>
    <div class="alarm-item mr-5">
      <ElButton type="danger" size="small" @click="toAlarmDetail(2)">
        严重
      </ElButton>
      <sup class="alarm-num-danger">{{ serious > 99 ? '99+' : serious }}</sup>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.btn-container {
  @applay w-full h-full flex items-center justify-between;
  .alarm-item {
    position: relative;
    // vertical-align: middle;
    display: inline-block;
    .alarm-num-success,
    .alarm-num-warning,
    .alarm-num-danger {
      position: absolute;
      color: hsl(var(--froreground));
      font-size: 12px;
      height: 18px;
      min-width: 18px;
      padding: 0 6px;
      border-radius: 10px;
      line-height: 18px;
      box-shadow: -1px 1px 5px hsl(var(--froreground));
      text-align: center;
      top: -10px;
      right: 10px;
      -webkit-transform: translateY(20%) translateX(90%);
      transform: translateY(20%) translateX(90%);
    }
    .alarm-num-success {
      background-color: #67c23a;
    }
    .alarm-num-warning {
      background-color: #e6a23c;
    }
    .alarm-num-danger {
      background-color: #f56c6c;
    }
  }
}
</style>
