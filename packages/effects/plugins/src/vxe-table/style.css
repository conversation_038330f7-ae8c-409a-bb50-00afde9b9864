:root .vxe-grid {
  --vxe-ui-font-color: hsl(var(--foreground));
  --vxe-ui-font-primary-color: hsl(var(--primary));

  /* --vxe-ui-font-lighten-color: #babdc0;
  --vxe-ui-font-darken-color: #86898e; */
  --vxe-ui-font-disabled-color: hsl(var(--foreground) / 50%);

  /* base */
  --vxe-ui-base-popup-border-color: hsl(var(--border));
  --vxe-ui-input-disabled-color: hsl(var(--border) / 60%);

  /* --vxe-ui-base-popup-box-shadow: 0px 12px 30px 8px rgb(0 0 0 / 50%); */

  /* layout */
  --vxe-ui-layout-background-color: hsl(var(--background));
  --vxe-ui-table-resizable-line-color: hsl(var(--heavy));

  /* --vxe-ui-table-fixed-left-scrolling-box-shadow: 8px 0px 10px -5px hsl(var(--accent));
  --vxe-ui-table-fixed-right-scrolling-box-shadow: -8px 0px 10px -5px hsl(var(--accent)); */

  /* input */
  --vxe-ui-input-border-color: hsl(var(--border));

  /* --vxe-ui-input-placeholder-color: #8d9095; */

  /* --vxe-ui-input-disabled-background-color: #262727; */

  /* loading */
  --vxe-ui-loading-background-color: hsl(var(--overlay-content));

  /* table */
  --vxe-ui-table-header-background-color: hsl(var(--accent));
  --vxe-ui-table-border-color: hsl(var(--border));
  --vxe-ui-table-row-hover-background-color: hsl(var(--accent-hover));
  --vxe-ui-table-row-striped-background-color: hsl(var(--accent) / 60%);
  --vxe-ui-table-row-hover-striped-background-color: hsl(var(--accent));
  --vxe-ui-table-row-radio-checked-background-color: hsl(var(--accent));
  --vxe-ui-table-row-hover-radio-checked-background-color: hsl(
    var(--accent-hover)
  );

  /* footer */
  --vxe-ui-table-footer-background-color: hsl(var(--muted));
  --vxe-ui-table-row-checkbox-checked-background-color: hsl(var(--accent));
  --vxe-ui-table-row-hover-checkbox-checked-background-color: hsl(
    var(--accent-hover)
  );
  --vxe-ui-table-row-current-background-color: hsl(var(--accent));
  --vxe-ui-table-row-hover-current-background-color: hsl(var(--accent-hover));
  --vxe-ui-font-primary-tinge-color: hsl(var(--primary));
  --vxe-ui-font-primary-lighten-color: hsl(var(--primary) / 60%);
  --vxe-ui-font-primary-darken-color: hsl(var(--primary));

  height: auto !important;

  /* --vxe-ui-table-fixed-scrolling-box-shadow-color: rgb(0 0 0 / 80%); */
}
:root[data-theme='tech-blue'].dark {
  --vxe-ui-font-color: hsl(var(--foreground));
}

.vxe-pager {
  .vxe-pager--prev-btn:not(.is--disabled):active,
  .vxe-pager--next-btn:not(.is--disabled):active,
  .vxe-pager--num-btn:not(.is--disabled):active,
  .vxe-pager--jump-prev:not(.is--disabled):active,
  .vxe-pager--jump-next:not(.is--disabled):active,
  .vxe-pager--prev-btn:not(.is--disabled):focus,
  .vxe-pager--next-btn:not(.is--disabled):focus,
  .vxe-pager--num-btn:not(.is--disabled):focus,
  .vxe-pager--jump-prev:not(.is--disabled):focus,
  .vxe-pager--jump-next:not(.is--disabled):focus {
    color: hsl(var(--accent-foreground));
    background-color: hsl(var(--accent));
    border: 1px solid hsl(var(--border));
    box-shadow: 0 0 0 1px hsl(var(--border));
  }

  .vxe-pager--wrapper {
    display: flex;
    align-items: center;
  }

  .vxe-pager--sizes {
    margin-right: auto;
  }
}

.vxe-pager--wrapper {
  @apply justify-center md:justify-end;
}

.vxe-tools--operate {
  margin-right: 0.25rem;
  margin-left: 0.75rem;
}

.vxe-table-custom--checkbox-option:hover {
  background: none !important;
}

.vxe-toolbar {
  padding: 0;
}

.vxe-buttons--wrapper:not(:empty),
.vxe-tools--operate:not(:empty),
.vxe-tools--wrapper:not(:empty) {
  padding: 0.6em 0;
}

.vxe-tools--operate:not(:has(button)) {
  margin-left: 0;
}

.vxe-grid--layout-header-wrapper {
  overflow: visible;
}

.vxe-grid--layout-body-content-wrapper {
  overflow: hidden;
}

/*深色模式下的表头背景色*/
:root[data-theme='tech-blue'].dark .vxe-grid {
  --vxe-ui-input-placeholder-color: hsl(var(--foreground) / 50%);
  --vxe-ui-font-primary-color: hsl(var(--foreground));
  --vxe-ui-modal-header-background-color: #47d;
  --vxe-ui-layout-background-color: hsl(var(--tab-deactive-bg));
  --vxe-ui-table-header-background-color: hsl(var(--accent-foreground));
  --vxe-ui-table-footer-background-color: hsl(var(--tab-deactive-bg));
  --vxe-ui-table-border-color: hsl(var(--accent-foreground));
  --vxe-ui-table-header-border-color: hsl(var(--vxe-header-border-color));
  --vxe-ui-table-row-hover-background-color: hsl(
    var(--vxe-row-hover-background-color)
  );
  --vxe-ui-font-color: hsl(var(--foreground));
  --vxe-ui-input-border-color: hsl(var(--accent-foreground));

  /* 斑马纹 */
  --vxe-ui-table-row-striped-background-color: hsl(var(--accent) / 60%);
  --vxe-ui-table-row-hover-striped-background-color: hsl(var(--accent));
  --vxe-ui-table-row-radio-checked-background-color: hsl(var(--accent));
  --vxe-ui-table-row-hover-radio-checked-background-color: hsl(
    var(--accent-hover)
  );
  --vxe-ui-table-row-checkbox-checked-background-color: hsl(
    var(--vxe-row-hover-background-color)
  );
  --vxe-ui-table-row-hover-checkbox-checked-background-color: hsl(
    var(--vxe-row-hover-background-color)
  );
  --vxe-ui-table-row-current-background-color: hsl(
    var(--vxe-row-hover-background-color)
  );
  --vxe-ui-table-row-hover-current-background-color: hsl(
    var(--vxe-row-hover-background-color)
  );
  --vxe-ui-font-primary-tinge-color: hsl(var(--primary));
  --vxe-ui-font-primary-lighten-color: hsl(var(--primary) / 60%);
  --vxe-ui-font-primary-darken-color: hsl(var(--primary));

  height: auto !important;

  /* --vxe-ui-table-fixed-scrolling-box-shadow-color: rgb(0 0 0 / 80%); */
}

/* 全局表格合计行样式 */
:root[data-theme='tech-blue'].dark .vxe-grid .vxe-footer--column {
  background-color: var(--vxe-ui-table-footer-background-color);
  font-weight: 600;
  border-right: 1px solid var(--vxe-ui-table-border-color);
}
/*科技蓝深色模式滚动条样式*/

:root[data-theme='tech-blue'].dark
  .vxe-table--render-default.border--default.sx-pos--bottom
  .vxe-table--scroll-x-wrapper::after,
:root[data-theme='tech-blue'].dark
  .vxe-table--render-default.border--full.sx-pos--bottom
  .vxe-table--scroll-x-wrapper::after,
:root[data-theme='tech-blue'].dark
  .vxe-table--render-default.border--outer.sx-pos--bottom
  .vxe-table--scroll-x-wrapper::after,
:root[data-theme='tech-blue'].dark
  .vxe-table--render-default.border--inner.sx-pos--bottom
  .vxe-table--scroll-x-wrapper::after {
  /*background-color: hsl(var(--card));*/
  border-top: var(--vxe-ui-table-border-width) solid
    var(--vxe-ui-table-border-color);
}
:root[data-theme='tech-blue'].dark .vxe-table .vxe-table--scroll-x-handle,
:root[data-theme='tech-blue'].dark .vxe-table .vxe-table--scroll-x-wrapper {
  /*background-color: hsl(var(--card));*/
}

/** 暗黑模式滚动条 */
:root[data-theme='tech-blue'].dark
  div.vxe-table--scroll-x-handle::-webkit-scrollbar,
:root[data-theme='tech-blue'].dark
  div.vxe-table--scroll-y-handle::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: hsl(var(--card)); /* or add it to the track */
}
:root[data-theme='tech-blue'].dark
  div.vxe-table--scroll-x-handle::-webkit-scrollbar-thumb,
:root[data-theme='tech-blue'].dark
  div.vxe-table--scroll-y-handle::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #47d;
}
:root[data-theme='tech-blue'].dark
  div.vxe-table--scroll-x-handle::-webkit-scrollbar-track,
:root[data-theme='tech-blue'].dark
  div.vxe-table--scroll-y-handle::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 0;
}

/*暗黑模式工具栏*/
:root[data-theme='tech-blue'].dark
  .vxe-button.type--button:not(.is--disabled):focus {
  border-color: hsl(var(--foreground));
}

:root[data-theme='tech-blue'].dark
  .vxe-button.type--button:not(.is--disabled):hover {
  color: hsl(var(--checked-text-color));
}
/*.暗黑模式表头样式 */
:root[data-theme='tech-blue'].dark .vxe-table .vxe-header--column {
  border-right: 1px solid var(--vxe-ui-table-header-border-color);
  border-bottom: 1px solid var(--vxe-ui-table-header-border-color);
  font-weight: 600;
  color: hsl(var(--foreground));
}

:root[data-theme='tech-blue'].dark .vxe-input--prefix-icon,
:root[data-theme='tech-blue'].dark .vxe-input--suffix-icon {
  color: hsl(var(--foreground));
}

:root[data-theme='tech-blue'].dark .vxe-modal--box {
}

:root[data-theme='tech-blue'].dark
  .vxe-modal--wrapper.type--modal
  .vxe-modal--header,
:root[data-theme='tech-blue'].dark
  .vxe-modal--wrapper.type--alert
  .vxe-modal--header,
:root[data-theme='tech-blue'].dark
  .vxe-modal--wrapper.type--confirm
  .vxe-modal--header {
  border-bottom: 1px solid var(--vxe-ui-table-header-border-color);
  background-color: var(--vxe-ui-modal-header-background-color);
}

:root[data-theme='tech-blue'].dark .vxe-select--panel-wrapper {
  border: 1px solid hsl(var(--accent-foreground));
  box-shadow: var(--vxe-ui-base-popup-box-shadow);
  background-color: hsl(var(--tab-deactive-bg));
}

:root[data-theme='tech-blue'].dark
  .vxe-select-option:not(.is--disabled).is--hover {
  background-color: hsl(var(--accent-foreground));
}

:root[data-theme='tech-blue'].dark .vxe-button.type--button {
  border-color: var(--vxe-ui-font-primary-color);
  background-color: hsl(var(--accent-foreground));
}

:root[data-theme='tech-blue'].dark
  .vxe-button.type--button.theme--primary:not(.is--disabled) {
  border-color: var(--vxe-ui-font-primary-color);
  background-color: hsl(var(--accent-foreground));
}
