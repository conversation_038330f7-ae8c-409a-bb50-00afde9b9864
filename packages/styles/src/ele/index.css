.el-card {
  --el-card-border-radius: var(--radius) !important;
}

.form-valid-error {
  /** select 选择器的样式 */
  .el-select .el-select__wrapper {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
  }

  /** input 选择器的样式 */
  .el-input .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
  }

  /** radio和checkbox 选择器的样式 */
  .el-radio .el-radio__inner,
  .el-checkbox .el-checkbox__inner {
    border: 1px solid var(--el-color-danger);
  }

  .el-checkbox-button .el-checkbox-button__inner,
  .el-radio-button .el-radio-button__inner {
    border: 1px solid var(--el-color-danger);
  }

  .el-checkbox-button:first-child .el-checkbox-button__inner,
  .el-radio-button:first-child .el-radio-button__inner {
    border-left: 1px solid var(--el-color-danger);
  }

  .el-checkbox-button:not(:first-child) .el-checkbox-button__inner,
  .el-radio-button:not(:first-child) .el-radio-button__inner {
    border-left: none;
  }

  .el-textarea .el-textarea__inner {
    border: 1px solid var(--el-color-danger);
  }
}
:root {
  --el-input-height: 34px;
}
html .el-loading-mask {
  z-index: 1000;
}
html[data-theme='tech-blue'].dark .el-tabs--left .el-tabs__header.is-left {
  background-color: hsl(var(--tab-deactive-bg));
  /*margin-right: 0;*/
}

html[data-theme='tech-blue'].dark .el-tabs__nav-wrap {
  background-color: hsl(var(--tab-deactive-bg));
}
html[data-theme='tech-blue'].dark .el-tabs__nav {
  /*background-color: hsl(var(--accent-foreground));*/
  background-color: hsl(var(--tab-deactive-bg));
}
html[data-theme='tech-blue'].dark .el-tabs__item {
  color: hsl(var(--foreground)) !important;
}

html[data-theme='tech-blue'].dark
  .el-tabs--border-card
  > .el-tabs__header
  .el-tabs__item.is-active {
  background-color: hsl(var(--accent-foreground));
}
html[data-theme='tech-blue'].dark
  .el-tabs--left.el-tabs--border-card
  .el-tabs__item.is-left {
  border: none;
  margin: 0;
}
html[data-theme='tech-blue'].dark .el-tabs--border-card {
  background: hsl(var(--card));
  border: 1px solid var(--el-border-color);
}
html[data-theme='tech-blue'].dark
  .el-tabs--border-card
  > .el-tabs__header
  .el-tabs__item.is-active {
  border-left-color: transparent;
  border-right-color: transparent;
  color: hsl(var(--foreground)) !important;
}
html[data-theme='tech-blue'].dark .el-tabs--border-card > .el-tabs__header {
  background-color: hsl(var(--tab-deactive-bg));
  border-bottom: 3px solid hsl(var(--header-border));
  margin: 0;
}
html[data-theme='tech-blue'].dark .el-input__wrapper{
  border: 1px solid hsl(var(--accent-foreground));
  color: hsl(var(--foreground));
  background-color: hsl(var(--card));
}
html[data-theme='tech-blue'].dark .el-date-editor.el-input,
html[data-theme='tech-blue'].dark .el-date-editor.el-input__wrapper {
  border: 1px solid hsl(var(--accent-foreground));
  color: hsl(var(--foreground));
  background-color: hsl(var(--card));
}

html[data-theme='tech-blue'].dark .el-date-range-picker {
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--accent-foreground));
  background-color: hsl(var(--card));
}

html[data-theme='tech-blue'].dark .el-date-table th {
  border-bottom: 1px solid hsl(var(--accent-foreground));
}

html[data-theme='tech-blue'].dark
  .el-date-table
  td.today
  .el-date-table-cell__text {
  color: hsl(var(--checked-text-color));
}

html[data-theme='tech-blue'].dark
  .el-date-table
  td.in-range
  .el-date-table-cell {
  background-color: hsl(var(--accent-foreground));
}

html[data-theme='tech-blue'].dark
  .el-date-table
  td.end-date
  .el-date-table-cell__text,
html[data-theme='tech-blue'].dark
  .el-date-table
  td.start-date
  .el-date-table-cell__text {
  background-color: hsl(var(--tab-deactive-bg));
}

html[data-theme='tech-blue'].dark .el-date-table td.available:hover {
  color: hsl(var(--checked-text-color));
}

html[data-theme='tech-blue'].dark .el-date-editor .el-range__icon {
  color: hsl(var(--foreground));
}

html[data-theme='tech-blue'].dark .el-date-editor .el-range__close-icon {
  color: hsl(var(--foreground));
}

html[data-theme='tech-blue'].dark .el-picker-panel__icon-btn:hover {
  color: hsl(var(--checked-text-color));
}

html[data-theme='tech-blue'].dark .el-popover.el-popper {
  color: hsl(var(--foreground));
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--accent-foreground));
}

html[data-theme='tech-blue'].dark .el-input__wrapper:hover {
  box-shadow: 0 0 0 1px var(--accent-foreground) inset;
}

html[data-theme='tech-blue'].dark .el-input__inner::placeholder {
  color: hsl(var(--foreground));
}

html[data-theme='tech-blue'].dark .el-input__suffix,
html[data-theme='tech-blue'].dark .el-input .el-input__clear,
html[data-theme='tech-blue'].dark .el-input .el-input__password {
  color: hsl(var(--foreground));
  cursor: pointer;
  font-size: 14px;
}

html[data-theme='tech-blue'].dark .el-button:hover {
  /*color: hsl(var(--checked-text-color));*/
}

html[data-theme='tech-blue'].dark  .el-popper.is-light>.el-popper__arrow:before {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--accent-foreground));
}
