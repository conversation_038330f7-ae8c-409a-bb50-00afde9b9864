name: Deploy to GitHub Pages

on:
  push:
    branches:
      - main
      - dev-release-0722  # 您当前的分支
  workflow_dispatch:  # 允许手动触发

# 设置 GITHUB_TOKEN 的权限以允许部署到 GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# 只允许一个并发部署，跳过正在运行和最新队列之间的运行队列
# 但是，不要取消正在进行的运行，因为我们希望让这些生产部署完成
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # 构建作业
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: ./.github/actions/setup-node

      - name: Setup Pages
        uses: actions/configure-pages@v4

      - name: Modify production config for GitHub Pages
        shell: bash
        run: |
          # 修改生产环境配置以适应 GitHub Pages
          sed -i "s#VITE_COMPRESS\s*=.*#VITE_COMPRESS = gzip#g" ./apps/web-ele/.env.production
          sed -i "s#VITE_PWA\s*=.*#VITE_PWA = true#g" ./apps/web-ele/.env.production
          # 设置正确的 base path（替换为您的仓库名）
          echo "VITE_BASE = /${{ github.event.repository.name }}/" >> ./apps/web-ele/.env.production
          cat ./apps/web-ele/.env.production

      - name: Build web-ele
        run: pnpm run build:ele

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./apps/web-ele/dist

  # 部署作业
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
