import { faker } from '@faker-js/faker/locale/zh_CN';

import { useResponseSuccess } from '~/utils/response';

export default eventHandler(async (event) => {
  const body = await readBody(event);
  const { camera_name, brand, serial_number, video_token, video_key, video_secret, region } = body;

  // 获取共享的 Mock 数据库
  if (!globalThis.CAMERA_MOCK_DATABASE) {
    globalThis.CAMERA_MOCK_DATABASE = [];
  }
  const database = globalThis.CAMERA_MOCK_DATABASE;

  // 生成新的 ID（确保唯一性）
  const maxId = database.length > 0 ? Math.max(...database.map(item => Number(item.id))) : 0;
  const newId = maxId + 1;

  // 模拟创建摄像头配置
  const newCamera = {
    id: newId,
    camera_name: camera_name || faker.company.name() + '摄像头',
    brand: brand || faker.helpers.arrayElement(['海康威视', '大华', '宇视', '华为', '天地伟业']),
    serial_number: serial_number || faker.string.alphanumeric(12).toUpperCase(),
    video_token: video_token || faker.string.uuid(),
    video_key: video_key || faker.string.alphanumeric(32),
    video_secret: video_secret || faker.string.alphanumeric(64),
    region: region || faker.helpers.arrayElement(['华北', '华东', '华南', '西南', '西北', '东北']),
    status: 1, // 默认启用状态
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  // 将新数据添加到共享数据库
  database.push(newCamera);

  // 模拟创建成功
  await sleep(500); // 模拟网络延迟

  return useResponseSuccess(newCamera, '摄像头配置创建成功');
});
