import { faker } from '@faker-js/faker/locale/zh_CN';

import { useResponseError, useResponseSuccess } from '~/utils/response';

export default eventHandler(async (event) => {
  const { id } = getRouterParams(event);
  const body = await readBody(event);
  const { camera_name, brand, serial_number, video_token, video_key, video_secret, region } = body;

  // 验证 ID 是否存在
  if (!id) {
    return useResponseError('摄像头配置 ID 不能为空', null, 400);
  }

  // 获取共享的 Mock 数据库
  if (!globalThis.CAMERA_MOCK_DATABASE) {
    globalThis.CAMERA_MOCK_DATABASE = [];
  }
  const database = globalThis.CAMERA_MOCK_DATABASE;

  // 查找要更新的记录
  const targetIndex = database.findIndex(item => String(item.id) === String(id));
  if (targetIndex === -1) {
    return useResponseError('摄像头配置不存在', null, 404);
  }

  const existingCamera = database[targetIndex];

  // 模拟更新摄像头配置
  const updatedCamera = {
    ...existingCamera, // 保留原有数据
    id: Number(id),
    camera_name: camera_name || existingCamera.camera_name,
    brand: brand || existingCamera.brand,
    serial_number: serial_number || existingCamera.serial_number,
    video_token: video_token || existingCamera.video_token,
    video_key: video_key || existingCamera.video_key,
    video_secret: video_secret || existingCamera.video_secret,
    region: region || existingCamera.region,
    updated_at: new Date().toISOString(),
  };

  // 更新数据库中的记录
  database[targetIndex] = updatedCamera;

  // 模拟更新成功
  await sleep(500); // 模拟网络延迟

  return useResponseSuccess(updatedCamera, '摄像头配置更新成功');
});
