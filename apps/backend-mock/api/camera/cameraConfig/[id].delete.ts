import { useResponseError, useResponseSuccess } from '~/utils/response';

export default eventHandler(async (event) => {
  const { id } = getRouterParams(event);

  // 验证 ID 是否存在
  if (!id) {
    return useResponseError('摄像头配置 ID 不能为空', null, 400);
  }

  // 获取共享的 Mock 数据库
  if (!globalThis.CAMERA_MOCK_DATABASE) {
    globalThis.CAMERA_MOCK_DATABASE = [];
  }
  const database = globalThis.CAMERA_MOCK_DATABASE;

  // 查找要删除的记录
  const targetIndex = database.findIndex(item => String(item.id) === String(id));
  if (targetIndex === -1) {
    return useResponseError('摄像头配置不存在', null, 404);
  }

  // 模拟一些不能删除的情况
  const protectedIds = ['1', '2']; // 模拟受保护的摄像头配置
  if (protectedIds.includes(String(id))) {
    return useResponseError('该摄像头配置正在使用中，无法删除', null, 400);
  }

  // 从数据库中删除记录
  database.splice(targetIndex, 1);

  // 模拟删除成功
  await sleep(300); // 模拟网络延迟

  return useResponseSuccess(null, '摄像头配置删除成功');
});
