import { faker } from '@faker-js/faker/locale/zh_CN';

import { useResponseSuccess } from '~/utils/response';

// 摄像头配置数据类型定义（与前端 CameraApi.CameraConfig 保持一致）
interface CameraConfigData {
  [key: string]: any;
  id: number | string;
  camera_name: string;
  brand: string;
  serial_number: string;
  video_token: string;
  video_key: string;
  video_secret: string;
  region: string;
}

// 请求参数类型定义
interface CameraListRequest {
  query?: {
    camera_name?: string;
    brand?: string;
    serial_number?: string;
  };
  pagination?: {
    page?: number;
    pageSize?: number;
  };
}

// 品牌列表
const BRANDS = ['Hikvision', 'Dahua', 'Uniview', 'Tiandy', 'Kedacom', 'Yushi', 'Sunell'];

// 区域列表
const REGIONS = ['华北', '华东', '华南', '华中', '西南', '西北', '东北'];

// 生成单条摄像头配置数据
function generateCameraConfigData(index: number): CameraConfigData {
  const brand = faker.helpers.arrayElement(BRANDS);
  const region = faker.helpers.arrayElement(REGIONS);

  return {
    id: index + 1, // id 类型为 number | string，这里使用 number
    camera_name: `摄像头${String(index + 1).padStart(3, '0')}`,
    brand,
    serial_number: faker.string.alphanumeric({ length: 12, casing: 'upper' }),
    video_token: `token_${faker.string.alphanumeric({ length: 16 })}`,
    video_key: `key_${faker.string.alphanumeric({ length: 12 })}`,
    video_secret: `secret_${faker.string.alphanumeric({ length: 20 })}`,
    region,
  };
}

// 生成大量Mock数据（模拟数据库中的数据）
function generateLargeMockData(count: number = 500): CameraConfigData[] {
  const data: CameraConfigData[] = [];
  for (let i = 0; i < count; i++) {
    data.push(generateCameraConfigData(i));
  }
  return data;
}

// 全局Mock数据（模拟数据库）- 使用 globalThis 确保跨文件共享
if (!globalThis.CAMERA_MOCK_DATABASE) {
  globalThis.CAMERA_MOCK_DATABASE = generateLargeMockData(500);
}
const MOCK_DATABASE = globalThis.CAMERA_MOCK_DATABASE;

// 多条件过滤函数
function filterData(
  data: CameraConfigData[],
  filters: {
    camera_name?: string;
    brand?: string;
    serial_number?: string;
  }
): CameraConfigData[] {
  let filteredData = [...data];

  // 摄像头名称过滤（模糊匹配）
  if (filters.camera_name && filters.camera_name.trim()) {
    filteredData = filteredData.filter(item =>
      item.camera_name.toLowerCase().includes(filters.camera_name!.toLowerCase())
    );
  }

  // 品牌过滤（模糊匹配）
  if (filters.brand && filters.brand.trim()) {
    filteredData = filteredData.filter(item =>
      item.brand.toLowerCase().includes(filters.brand!.toLowerCase())
    );
  }

  // 序列号过滤（模糊匹配）
  if (filters.serial_number && filters.serial_number.trim()) {
    filteredData = filteredData.filter(item =>
      item.serial_number.toLowerCase().includes(filters.serial_number!.toLowerCase())
    );
  }

  return filteredData;
}

// 分页函数
function paginateData(
  data: CameraConfigData[],
  page: number = 1,
  pageSize: number = 10
): {
  items: CameraConfigData[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
} {
  const total = data.length;
  const totalPages = Math.ceil(total / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const items = data.slice(startIndex, endIndex);

  return {
    items,
    total,
    page,
    pageSize,
    totalPages,
  };
}

export default defineEventHandler(async (event) => {
  console.log('摄像头配置列表API调用 (POST)');

  // 获取POST请求体数据
  const body: CameraListRequest = await readBody(event);
  const { query = {}, pagination = {} } = body;

  // 解构查询条件
  const { camera_name, brand, serial_number } = query;

  // 解构分页参数，提供默认值
  const { page = 1, pageSize = 10 } = pagination;

  console.log('摄像头配置查询参数:', {
    query: { camera_name, brand, serial_number },
    pagination: { page, pageSize },
  });

  // 多条件过滤数据
  const filteredData = filterData(MOCK_DATABASE, {
    camera_name,
    brand,
    serial_number,
  });

  console.log(`过滤后数据条数: ${filteredData.length}`);

  // 分页处理
  const paginatedResult = paginateData(filteredData, page, pageSize);

  console.log(`分页结果: 第${paginatedResult.page}页，每页${paginatedResult.pageSize}条，共${paginatedResult.total}条，共${paginatedResult.totalPages}页`);

  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  return useResponseSuccess({
    items: paginatedResult.items,
    total: paginatedResult.total,
    page: paginatedResult.page,
    pageSize: paginatedResult.pageSize,
    totalPages: paginatedResult.totalPages,
  });
});
