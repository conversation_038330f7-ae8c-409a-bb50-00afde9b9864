import { faker } from '@faker-js/faker/locale/zh_CN';

import { useResponseSuccess } from '~/utils/response';

// 树形节点数据类型定义（与前端 TreeNodeApi.TreeNode 保持一致）
interface TreeNodeData {
  [key: string]: any;
  id: string;
  label: string;
  parentId?: string | null;
  level: number; // 节点层级：1-4
  type: 'group' | 'camera'; // 节点类型：分组或摄像头
  icon?: string;
  status?: number; // 状态：1-启用，0-禁用
  sort?: number; // 排序
  children?: TreeNodeData[];
  created_at?: string;
  updated_at?: string;
}

// 请求参数类型定义
interface TreeNodeRequest {
  query?: {
    label?: string;
    type?: 'group' | 'camera';
    level?: number;
    parentId?: string;
    status?: number;
  };
}

// 生成四级树形结构的Mock数据
function generateTreeNodeMockData(): TreeNodeData[] {
  const now = new Date().toISOString();

  return [
    // 第一级：电能站点
    {
      id: '1',
      label: '电能站点',
      parentId: null,
      level: 1,
      type: 'group',
      icon: 'mdi:flash',
      status: 1,
      sort: 1,
      created_at: now,
      updated_at: now,
      children: [
        // 第二级：智慧保温科技
        {
          id: '1-1',
          label: '智慧保温科技',
          parentId: '1',
          level: 2,
          type: 'group',
          icon: 'mdi:factory',
          status: 1,
          sort: 1,
          created_at: now,
          updated_at: now,
          children: [
            // 第三级：二级
            {
              id: '1-1-1',
              label: '二级',
              parentId: '1-1',
              level: 3,
              type: 'group',
              icon: 'mdi:folder',
              status: 1,
              sort: 1,
              created_at: now,
              updated_at: now,
              children: [
                // 第四级：南配电房其余设备
                {
                  id: '1-1-1-1',
                  label: '南配电房其余设备',
                  parentId: '1-1-1',
                  level: 4,
                  type: 'group',
                  icon: 'mdi:folder-outline',
                  status: 1,
                  sort: 1,
                  created_at: now,
                  updated_at: now,
                  children: [
                    // 摄像头设备
                    {
                      id: '1-1-1-1-1',
                      label: 'N-D2-5-1(纸箱打包机)',
                      parentId: '1-1-1-1',
                      level: 4,
                      type: 'camera',
                      icon: 'mdi:cctv',
                      status: 1,
                      sort: 1,
                      created_at: now,
                      updated_at: now,
                    },
                  ],
                },
                // 更多第四级节点
                {
                  id: '1-1-1-2',
                  label: '北配电房设备',
                  parentId: '1-1-1',
                  level: 4,
                  type: 'group',
                  icon: 'mdi:folder-outline',
                  status: 1,
                  sort: 2,
                  created_at: now,
                  updated_at: now,
                  children: [
                    {
                      id: '1-1-1-2-1',
                      label: 'N-D2-5-2(包装生产线)',
                      parentId: '1-1-1-2',
                      level: 4,
                      type: 'camera',
                      icon: 'mdi:cctv',
                      status: 1,
                      sort: 1,
                      created_at: now,
                      updated_at: now,
                    },
                    {
                      id: '1-1-1-2-2',
                      label: 'N-D2-5-3(质检设备)',
                      parentId: '1-1-1-2',
                      level: 4,
                      type: 'camera',
                      icon: 'mdi:cctv',
                      status: 1,
                      sort: 2,
                      created_at: now,
                      updated_at: now,
                    },
                  ],
                },
              ],
            },
            // 更多第三级节点
            {
              id: '1-1-2',
              label: '三级',
              parentId: '1-1',
              level: 3,
              type: 'group',
              icon: 'mdi:folder',
              status: 1,
              sort: 2,
              created_at: now,
              updated_at: now,
              children: [
                {
                  id: '1-1-2-1',
                  label: '生产车间监控',
                  parentId: '1-1-2',
                  level: 4,
                  type: 'group',
                  icon: 'mdi:folder-outline',
                  status: 1,
                  sort: 1,
                  created_at: now,
                  updated_at: now,
                  children: [
                    {
                      id: '1-1-2-1-1',
                      label: 'N-D3-1-1(生产线A)',
                      parentId: '1-1-2-1',
                      level: 4,
                      type: 'camera',
                      icon: 'mdi:cctv',
                      status: 1,
                      sort: 1,
                      created_at: now,
                      updated_at: now,
                    },
                    {
                      id: '1-1-2-1-2',
                      label: 'N-D3-1-2(生产线B)',
                      parentId: '1-1-2-1',
                      level: 4,
                      type: 'camera',
                      icon: 'mdi:cctv',
                      status: 1,
                      sort: 2,
                      created_at: now,
                      updated_at: now,
                    },
                  ],
                },
              ],
            },
          ],
        },
        // 更多第二级节点
        {
          id: '1-2',
          label: '办公区域',
          parentId: '1',
          level: 2,
          type: 'group',
          icon: 'mdi:office-building',
          status: 1,
          sort: 2,
          created_at: now,
          updated_at: now,
          children: [
            {
              id: '1-2-1',
              label: '一楼',
              parentId: '1-2',
              level: 3,
              type: 'group',
              icon: 'mdi:folder',
              status: 1,
              sort: 1,
              created_at: now,
              updated_at: now,
              children: [
                {
                  id: '1-2-1-1',
                  label: '大厅监控',
                  parentId: '1-2-1',
                  level: 4,
                  type: 'group',
                  icon: 'mdi:folder-outline',
                  status: 1,
                  sort: 1,
                  created_at: now,
                  updated_at: now,
                  children: [
                    {
                      id: '1-2-1-1-1',
                      label: 'N-O1-1-1(前台监控)',
                      parentId: '1-2-1-1',
                      level: 4,
                      type: 'camera',
                      icon: 'mdi:cctv',
                      status: 1,
                      sort: 1,
                      created_at: now,
                      updated_at: now,
                    },
                    {
                      id: '1-2-1-1-2',
                      label: 'N-O1-1-2(会客区监控)',
                      parentId: '1-2-1-1',
                      level: 4,
                      type: 'camera',
                      icon: 'mdi:cctv',
                      status: 1,
                      sort: 2,
                      created_at: now,
                      updated_at: now,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ];
}

// 全局Mock数据（模拟数据库）- 使用 globalThis 确保跨文件共享
if (!globalThis.TREE_NODE_MOCK_DATABASE) {
  globalThis.TREE_NODE_MOCK_DATABASE = generateTreeNodeMockData();
}
const MOCK_DATABASE = globalThis.TREE_NODE_MOCK_DATABASE;

// 递归过滤函数
function filterTreeNodes(nodes: TreeNodeData[], query: any): TreeNodeData[] {
  return nodes.filter(node => {
    // 检查当前节点是否匹配
    let matches = true;

    if (query.label && !node.label.includes(query.label)) {
      matches = false;
    }
    if (query.type && node.type !== query.type) {
      matches = false;
    }
    if (query.level && node.level !== query.level) {
      matches = false;
    }
    if (query.parentId && node.parentId !== query.parentId) {
      matches = false;
    }
    if (query.status !== undefined && node.status !== query.status) {
      matches = false;
    }

    // 递归过滤子节点
    if (node.children && node.children.length > 0) {
      const filteredChildren = filterTreeNodes(node.children, query);
      if (filteredChildren.length > 0) {
        node.children = filteredChildren;
        matches = true; // 如果有子节点匹配，父节点也应该显示
      } else if (!matches) {
        return false;
      }
    }

    return matches;
  });
}

export default defineEventHandler(async (event) => {
  console.log('树形节点列表API调用 (POST)');

  // 获取POST请求体数据，处理空请求体的情况
  let body: TreeNodeRequest = {};
  try {
    body = await readBody(event) || {};
  } catch (error) {
    console.log('请求体为空，使用默认值');
  }

  const { query = {} } = body || {};

  console.log('树形节点查询参数:', { query });

  // 模拟网络延迟
  await sleep(300);

  // 过滤数据
  let filteredData = MOCK_DATABASE;
  if (Object.keys(query).length > 0) {
    filteredData = filterTreeNodes([...MOCK_DATABASE], query);
  }

  console.log('树形节点数据查询完成，节点数量:', filteredData.length);

  return useResponseSuccess({
    items: filteredData,
    total: filteredData.length,
  });
});

// 辅助函数：模拟延迟
function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
