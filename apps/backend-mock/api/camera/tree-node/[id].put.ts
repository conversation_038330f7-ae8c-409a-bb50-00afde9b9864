import { useResponseError, useResponseSuccess } from '~/utils/response';

// 树形节点数据类型定义
interface TreeNodeData {
  [key: string]: any;
  id: string;
  label: string;
  parentId?: string | null;
  level: number;
  type: 'group' | 'camera';
  icon?: string;
  status?: number;
  sort?: number;
  children?: TreeNodeData[];
  created_at?: string;
  updated_at?: string;
}

// 更新节点的请求数据
interface UpdateTreeNodeRequest {
  label?: string;
  parentId?: string | null;
  type?: 'group' | 'camera';
  icon?: string;
  status?: number;
  sort?: number;
}

// 递归查找节点
function findNodeById(nodes: TreeNodeData[], id: string): TreeNodeData | null {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
}

// 递归更新节点
function updateNodeById(nodes: TreeNodeData[], id: string, updateData: UpdateTreeNodeRequest): boolean {
  for (const node of nodes) {
    if (node.id === id) {
      // 更新节点数据
      if (updateData.label !== undefined) node.label = updateData.label;
      if (updateData.type !== undefined) node.type = updateData.type;
      if (updateData.icon !== undefined) node.icon = updateData.icon;
      if (updateData.status !== undefined) node.status = updateData.status;
      if (updateData.sort !== undefined) node.sort = updateData.sort;
      node.updated_at = new Date().toISOString();
      return true;
    }
    if (node.children && node.children.length > 0) {
      if (updateNodeById(node.children, id, updateData)) {
        return true;
      }
    }
  }
  return false;
}

export default eventHandler(async (event) => {
  const { id } = getRouterParams(event);
  const body: UpdateTreeNodeRequest = await readBody(event);

  // 验证 ID 是否存在
  if (!id) {
    return useResponseError('节点 ID 不能为空', null, 400);
  }

  // 验证类型（如果提供）
  if (body.type && !['group', 'camera'].includes(body.type)) {
    return useResponseError('节点类型必须是 group 或 camera', null, 400);
  }

  // 获取共享的 Mock 数据库
  if (!globalThis.TREE_NODE_MOCK_DATABASE) {
    globalThis.TREE_NODE_MOCK_DATABASE = [];
  }
  const database = globalThis.TREE_NODE_MOCK_DATABASE;

  // 查找要更新的节点
  const targetNode = findNodeById(database, id);
  if (!targetNode) {
    return useResponseError('节点不存在', null, 404);
  }

  // 更新节点
  const success = updateNodeById(database, id, body);
  if (!success) {
    return useResponseError('更新节点失败', null, 500);
  }

  // 获取更新后的节点
  const updatedNode = findNodeById(database, id);

  // 模拟网络延迟
  await sleep(500);

  console.log('更新树形节点成功:', updatedNode);

  return useResponseSuccess(updatedNode, '节点更新成功');
});

// 辅助函数：模拟延迟
function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
