import { useResponseError, useResponseSuccess } from '~/utils/response';

// 树形节点数据类型定义
interface TreeNodeData {
  [key: string]: any;
  id: string;
  label: string;
  parentId?: string | null;
  level: number;
  type: 'group' | 'camera';
  icon?: string;
  status?: number;
  sort?: number;
  children?: TreeNodeData[];
  created_at?: string;
  updated_at?: string;
}

// 递归查找节点
function findNodeById(nodes: TreeNodeData[], id: string): TreeNodeData | null {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
}

// 递归删除节点
function deleteNodeById(nodes: TreeNodeData[], id: string): boolean {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === id) {
      nodes.splice(i, 1);
      return true;
    }
    if (nodes[i].children && nodes[i].children!.length > 0) {
      if (deleteNodeById(nodes[i].children!, id)) {
        return true;
      }
    }
  }
  return false;
}

// 递归统计子节点数量
function countChildNodes(node: TreeNodeData): number {
  let count = 0;
  if (node.children && node.children.length > 0) {
    count += node.children.length;
    for (const child of node.children) {
      count += countChildNodes(child);
    }
  }
  return count;
}

export default eventHandler(async (event) => {
  const { id } = getRouterParams(event);

  // 验证 ID 是否存在
  if (!id) {
    return useResponseError('节点 ID 不能为空', null, 400);
  }

  // 获取共享的 Mock 数据库
  if (!globalThis.TREE_NODE_MOCK_DATABASE) {
    globalThis.TREE_NODE_MOCK_DATABASE = [];
  }
  const database = globalThis.TREE_NODE_MOCK_DATABASE;

  // 查找要删除的节点
  const targetNode = findNodeById(database, id);
  if (!targetNode) {
    return useResponseError('节点不存在', null, 404);
  }

  // 模拟一些不能删除的情况
  const protectedIds = ['1', '1-1']; // 模拟受保护的节点
  if (protectedIds.includes(id)) {
    return useResponseError('该节点为系统保护节点，无法删除', null, 400);
  }

  // 检查是否有子节点
  const childCount = countChildNodes(targetNode);
  if (childCount > 0) {
    return useResponseError(`该节点下还有 ${childCount} 个子节点，请先删除子节点`, null, 400);
  }

  // 如果是摄像头节点，检查是否正在使用中
  if (targetNode.type === 'camera') {
    // 模拟一些正在使用的摄像头
    const busyCameras = ['1-1-1-1-1', '1-2-1-1-1'];
    if (busyCameras.includes(id)) {
      return useResponseError('该摄像头正在使用中，无法删除', null, 400);
    }
  }

  // 执行删除操作
  const success = deleteNodeById(database, id);
  if (!success) {
    return useResponseError('删除节点失败', null, 500);
  }

  // 模拟网络延迟
  await sleep(500);

  console.log('删除树形节点成功:', { id, label: targetNode.label });

  return useResponseSuccess({ id }, '节点删除成功');
});

// 辅助函数：模拟延迟
function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
