import { faker } from '@faker-js/faker/locale/zh_CN';

import { useResponseError, useResponseSuccess } from '~/utils/response';

// 树形节点数据类型定义
interface TreeNodeData {
  [key: string]: any;
  id: string;
  label: string;
  parentId?: string | null;
  level: number;
  type: 'group' | 'camera';
  icon?: string;
  status?: number;
  sort?: number;
  children?: TreeNodeData[];
  created_at?: string;
  updated_at?: string;
}

// 创建节点的请求数据
interface CreateTreeNodeRequest {
  label: string;
  parentId?: string | null;
  type: 'group' | 'camera';
  icon?: string;
  status?: number;
  sort?: number;
}

// 递归查找节点
function findNodeById(nodes: TreeNodeData[], id: string): TreeNodeData | null {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
}

// 递归添加节点到指定父节点
function addNodeToParent(nodes: TreeNodeData[], parentId: string, newNode: TreeNodeData): boolean {
  for (const node of nodes) {
    if (node.id === parentId) {
      if (!node.children) {
        node.children = [];
      }
      node.children.push(newNode);
      return true;
    }
    if (node.children && node.children.length > 0) {
      if (addNodeToParent(node.children, parentId, newNode)) {
        return true;
      }
    }
  }
  return false;
}

// 生成唯一ID
function generateUniqueId(database: TreeNodeData[]): string {
  const allIds = new Set<string>();
  
  function collectIds(nodes: TreeNodeData[]) {
    for (const node of nodes) {
      allIds.add(node.id);
      if (node.children && node.children.length > 0) {
        collectIds(node.children);
      }
    }
  }
  
  collectIds(database);
  
  let newId: string;
  do {
    newId = faker.string.alphanumeric(8);
  } while (allIds.has(newId));
  
  return newId;
}

export default eventHandler(async (event) => {
  const body: CreateTreeNodeRequest = await readBody(event);
  const { label, parentId, type, icon, status = 1, sort = 1 } = body;

  // 验证必填字段
  if (!label || !type) {
    return useResponseError('节点名称和类型不能为空', null, 400);
  }

  // 验证类型
  if (!['group', 'camera'].includes(type)) {
    return useResponseError('节点类型必须是 group 或 camera', null, 400);
  }

  // 获取共享的 Mock 数据库
  if (!globalThis.TREE_NODE_MOCK_DATABASE) {
    globalThis.TREE_NODE_MOCK_DATABASE = [];
  }
  const database = globalThis.TREE_NODE_MOCK_DATABASE;

  // 计算节点层级
  let level = 1;
  if (parentId) {
    const parentNode = findNodeById(database, parentId);
    if (!parentNode) {
      return useResponseError('父节点不存在', null, 404);
    }
    level = parentNode.level + 1;
    
    // 验证层级不超过4级
    if (level > 4) {
      return useResponseError('节点层级不能超过4级', null, 400);
    }
  }

  // 生成新节点
  const now = new Date().toISOString();
  const newNode: TreeNodeData = {
    id: generateUniqueId(database),
    label,
    parentId: parentId || null,
    level,
    type,
    icon: icon || (type === 'camera' ? 'mdi:cctv' : 'mdi:folder'),
    status,
    sort,
    children: type === 'group' ? [] : undefined,
    created_at: now,
    updated_at: now,
  };

  // 添加节点到数据库
  if (parentId) {
    // 添加到指定父节点
    const success = addNodeToParent(database, parentId, newNode);
    if (!success) {
      return useResponseError('添加节点到父节点失败', null, 500);
    }
  } else {
    // 添加为根节点
    database.push(newNode);
  }

  // 模拟网络延迟
  await sleep(500);

  console.log('创建树形节点成功:', newNode);

  return useResponseSuccess(newNode, '节点创建成功');
});

// 辅助函数：模拟延迟
function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
