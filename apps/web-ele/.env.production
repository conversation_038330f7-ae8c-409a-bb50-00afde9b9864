VITE_BASE=/

# 接口地址 - 使用相对路径，让Nginx代理到Mock服务
VITE_GLOB_API_URL=/api

# 是否开启压缩，可以设置为 none, brotli, gzip
VITE_COMPRESS = gzip

# 是否开启 PWA
VITE_PWA = true

# vue-router 的模式
VITE_ROUTER_HISTORY=hash

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true

# 打包后是否生成dist.zip
VITE_ARCHIVER=true

# 监控地址
VITE_APP_MONITRO_ADMIN=http://localhost:9090/admin/login

# xxl-job 控制台地址
VITE_APP_XXL_JOB_ADMIN=http://localhost:9100/xxl-job-admin

# 路由懒加载
VITE_CLI_BABEL_TRANSPILE_MODULES=true
