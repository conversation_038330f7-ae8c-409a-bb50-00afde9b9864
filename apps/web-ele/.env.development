# 端口号
VITE_PORT=5777

VITE_BASE=/

# 接口地址
# VITE_GLOB_API_URL=http://************:9001/api
VITE_GLOB_API_URL=/api

# 是否开启 Nitro Mock服务，true 为开启，false 为关闭
VITE_NITRO_MOCK=true

# 是否打开 devtools，true 为打开，false 为关闭
VITE_DEVTOOLS=true

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true

# 监控地址
VITE_APP_MONITRO_ADMIN=http://localhost:9090/admin/login

# xxl-job 控制台地址
VITE_APP_XXL_JOB_ADMIN=http://localhost:9100/xxl-job-admin

# 路由懒加载
VITE_CLI_BABEL_TRANSPILE_MODULES=true
