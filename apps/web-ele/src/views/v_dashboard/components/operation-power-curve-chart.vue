<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts, getChartInstance } = useEcharts(chartRef);

// 生成时间轴数据 (24小时，每2小时10分钟一个点)
const generateTimeData = () => {
  const times = [];
  for (let i = 0; i < 24; i += 2) {
    const hour = i.toString().padStart(2, '0');
    const minute = (i * 10) % 60;
    const minuteStr = minute.toString().padStart(2, '0');
    times.push(`${hour}:${minuteStr}`);
  }
  return times;
};

// 生成假数据 - 双向正负值
const generateMockData = (name: string) => {
  const baseValues = {
    负荷: [25, 30, 28, 35, 40, 38, 42, 45, 48, 46, 50, 52], // 负荷通常为正值
    储能: [-15, 20, -18, 25, -22, 28, -25, 30, -28, 32, -30, 35], // 储能有充放电，正负交替
    市电: [10, 15, 12, 18, 20, 22, 25, 28, 30, 32, 35, 38], // 市电通常为正值
    新能源: [0, 5, 8, 15, 25, 35, 40, 38, 30, 20, 10, 5], // 新能源白天高，夜晚低
  };
  return baseValues[name as keyof typeof baseValues] || [];
};

onMounted(() => {
  const timeData = generateTimeData();

  renderEcharts({
    grid: {
      top: '15%', // 为图例留出空间
      left: '8%', // 为Y轴标签留出空间
      right: '5%', // 右侧留出少量空间
      bottom: '10%', // 为X轴标签留出空间
      containLabel: true, // 确保标签不会被裁剪
    },
    series: [
      {
        name: '负荷',
        data: generateMockData('负荷'),
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#ef4444', // red-500
          width: 2,
        },
        itemStyle: {
          color: '#ef4444',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
      {
        name: '储能',
        data: generateMockData('储能'),
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#10b981', // green-500
          width: 2,
        },
        itemStyle: {
          color: '#10b981',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
      {
        name: '市电',
        data: generateMockData('市电'),
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#3b82f6', // blue-500
          width: 2,
        },
        itemStyle: {
          color: '#3b82f6',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
      {
        name: '新能源',
        data: generateMockData('新能源'),
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#f59e0b', // amber-500
          width: 2,
        },
        itemStyle: {
          color: '#f59e0b',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
    ],
    legend: {
      right: '5%',
      top: '0%',
      data: ['负荷', '储能', '市电', '新能源'],
      textStyle: {
        color: '#6b7280',
        fontSize: 11,
      },
    },
    tooltip: {
      axisPointer: {
        lineStyle: {
          color: '#3b82f6',
          width: 1,
        },
      },
      trigger: 'axis',
      formatter(params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          const value = param.value;
          const unit = 'kW';
          result += `${param.marker}${param.seriesName}: ${value} ${unit}<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      data: timeData,
      type: 'category',
      axisLine: {
        lineStyle: {
          color: '#e5e7eb',
        },
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
        formatter: '{value} kW',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      // 设置Y轴范围，支持正负值
      min: -40,
      max: 60,
    },
  });
});
</script>

<template>
  <div class="operation-power-curve-chart flex h-full w-full flex-col">
    <EchartsUI ref="chartRef" width="100%" />
  </div>
</template>

<style scoped>
.operation-power-curve-chart {
}
</style>
