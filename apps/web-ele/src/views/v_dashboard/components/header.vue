<script setup lang="ts">
import type { DateDataType } from './index.d.ts';

import { reactive, ref } from 'vue';

import dayjs from 'dayjs';

import headerBg from '../assets/<EMAIL>';
import headerBgNon from '../assets/<EMAIL>';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});

const defaultTitle: string = ref('');
const dateData = reactive<DateDataType>({
  dateDay: '',
  dateYear: '',
  dateWeek: '',
  time: '',
  timing: null,
});

// const { setSettingShow } = useSettingStore();
const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
const timeFn = () => {
  dateData.timing = setInterval(() => {
    // 'YYYY-MM-DD hh : mm : ss'
    dateData.dateDay = dayjs().format('YYYY-MM-DD');
    dateData.time = dayjs().format('hh : mm : ss');
    dateData.dateWeek = weekday[dayjs().day()];
  }, 1000);
};

const handleSettingClick = () => {
  console.log('设置按钮被点击');
  // 这里可以添加设置相关的逻辑
};

timeFn();
</script>

<template>
  <div class="relative flex h-[90px] w-full">
    <slot name="left">
      <div
        class="date-time-wrap text-white absolute left-[1.56%] top-[28.88%] flex text-[24px] font-normal leading-[34px] tracking-normal opacity-100"
      >
        <div class="date mr-[30px]">{{ dateData.dateDay }}</div>
        <div class="time mr-[30px]">{{ dateData.time }}</div>
        <div class="week">{{ dateData.dateWeek }}</div>
      </div>
    </slot>
    <div
      class="absolutee left-0 right-0 top-0 mx-auto mb-[11px] h-[90px] w-[83.33%] bg-top bg-no-repeat opacity-100"
      :style="{
        backgroundImage: `url(${title ? headerBgNon : headerBg})`,
        backgroundSize: 'cover',
      }"
    >
      <slot v-if="title">{{ title }}</slot>
    </div>
    <slot name="right"></slot>
  </div>
</template>
<style scoped lang="scss">
.date-time-wrap {
  font-family: 'PingFang SC';
}
.top_title {
}
</style>
