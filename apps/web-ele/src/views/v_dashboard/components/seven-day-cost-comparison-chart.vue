<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { computed, onMounted, onUnmounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts, getChartInstance } = useEcharts(chartRef);

// 计算图表容器的高度
const chartHeight = computed(() => '100%');
// 生成近七日日期数据
const generateDateData = () => {
  const dates = [];
  const today = new Date();
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    dates.push(`${month}-${day}`);
  }
  return dates;
};

// 生成假数据 - 费用对比
const generateMockData = () => {
  const optimizedBefore = [3000, 2600, 3800, 3000, 4200, 4200, 4200]; // 优化前费用（更高）
  const optimizedAfter = [800, 1200, 600, 900, 1100, 1300, 1000]; // 优化后费用（更低）

  // 计算优化节省的费用（用于堆叠显示）
  const optimizedSavings = optimizedBefore.map(
    (before, index) => before - optimizedAfter[index],
  );

  return {
    optimizedBefore,
    optimizedAfter,
    optimizedSavings, // 节省的费用，用于堆叠显示
  };
};

// 3D柱状图颜色配置
const getBarColors = () => {
  return [
    // 优化后（蓝色）渐变
    {
      type: 'linear',
      x: 0,
      x2: 1,
      y: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: '#2563eb', // 深蓝
        },
        {
          offset: 0.5,
          color: '#2563eb',
        },
        {
          offset: 0.5,
          color: '#3b82f6', // 浅蓝
        },
        {
          offset: 1,
          color: '#3b82f6',
        },
      ],
    },
    // 优化前（橙色）渐变
    {
      type: 'linear',
      x: 0,
      x2: 1,
      y: 0,
      y2: 0,
      colorStops: [
        {
          offset: 0,
          color: '#d97706', // 深橙
        },
        {
          offset: 0.5,
          color: '#d97706',
        },
        {
          offset: 0.5,
          color: '#f59e0b', // 浅橙
        },
        {
          offset: 1,
          color: '#f59e0b',
        },
      ],
    },
  ];
};

// 菱形顶部专用颜色配置（模拟3D顶面效果）
const getDiamondTopColors = () => {
  return [
    // 优化后菱形顶部（蓝色系，使用径向渐变模拟3D效果）
    {
      type: 'radial',
      x: 0.5,
      y: 0.3, // 光源位置偏上
      r: 0.8,
      colorStops: [
        {
          offset: 0,
          color: '#60a5fa', // 亮蓝色（光照区域）
        },
        {
          offset: 0.7,
          color: '#3b82f6', // 中蓝色
        },
        {
          offset: 1,
          color: '#2563eb', // 深蓝色（阴影区域）
        },
      ],
    },
    // 优化前菱形顶部（橙色系，使用径向渐变模拟3D效果）
    {
      type: 'radial',
      x: 0.5,
      y: 0.3, // 光源位置偏上
      r: 0.8,
      colorStops: [
        {
          offset: 0,
          color: '#fbbf24', // 亮橙色（光照区域）
        },
        {
          offset: 0.7,
          color: '#f59e0b', // 中橙色
        },
        {
          offset: 1,
          color: '#d97706', // 深橙色（阴影区域）
        },
      ],
    },
  ];
};

// 生成3D柱状图所需的辅助数据
const generate3DBarData = (mockData) => {
  const barWidth = 30;
  const diamondWidth = 32; // 菱形宽度略大于柱体，更好贴合
  const diamondHeight = 10; // 菱形高度调小，更贴合柱体
  const colors = getBarColors();
  const diamondColors = getDiamondTopColors();
  const constData = []; // 底部菱形数据
  const topData = []; // 顶部菱形数据

  for (let i = 0; i < mockData.optimizedBefore.length; i++) {
    // 底部菱形（固定高度）- 使用与蓝色柱体相同的线性渐变
    constData.push({
      value: 1,
      itemStyle: {
        color: colors[0], // 使用与优化后柱体相同的蓝色线性渐变
      },
    });

    // 顶部菱形（总高度 = 优化前的费用）- 使用径向渐变模拟3D顶面
    const totalValue = mockData.optimizedBefore[i];
    topData.push({
      value: totalValue,
      itemStyle: {
        color: diamondColors[1], // 使用专门的菱形顶部颜色，无边框
      },
    });
  }

  return {
    barWidth,
    diamondWidth,
    diamondHeight,
    colors,
    diamondColors,
    constData,
    topData,
  };
};

onMounted(async () => {
  const dateData = generateDateData();
  const mockData = generateMockData();
  const {
    barWidth,
    diamondWidth,
    diamondHeight,
    colors,
    diamondColors,
    constData,
    topData,
  } = generate3DBarData(mockData);

  // 先渲染图表
  await renderEcharts({
    // animationDuration: 1500,
    // animationEasing: 'cubicOut',
    // animationDelay: (idx) => idx * 100,

    grid: {
      top: '15%',    // 为图例留出空间
      left: '8%',    // 为Y轴标签留出空间
      right: '5%',   // 右侧留出少量空间
      bottom: '12%', // 为X轴标签留出空间
      containLabel: true, // 确保标签不会被裁剪
    },
    legend: {
      right: '5%',
      top: '0%',
      data: [
        {
          name: '优化后',
          itemStyle: {
            color: '#3b82f6', // 蓝色纯色
          },
        },
        {
          name: '优化前',
          itemStyle: {
            color: '#f59e0b', // 橙色纯色
          },
        },
      ],
      textStyle: {
        color: '#ffffff',
        fontSize: 12,
      },
      itemWidth: 20,
      itemHeight: 10,
      itemGap: 10,
      selectedMode: false, // 禁用图例点击切换功能
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      formatter(params: any) {
        const validParams = params.filter(
          (param: any) =>
            param.seriesName === '优化前' || param.seriesName === '优化后',
        );

        if (validParams.length === 0) return '';

        let result = `${params[0].axisValue}<br/>`;
        let optimizedAfter = 0;
        let optimizedBefore = 0;

        validParams.forEach((param: any) => {
          if (param.seriesName === '优化后') {
            optimizedAfter = param.value;
          } else if (param.seriesName === '优化前') {
            // 这里显示的是节省的费用，需要加上优化后的费用得到优化前的总费用
            optimizedBefore = optimizedAfter + param.value;
          }
        });

        result += `<span style="color: #3b82f6;">●</span> 优化后: ${optimizedAfter} 元<br/>`;
        result += `<span style="color: #f59e0b;">●</span> 优化前: ${optimizedBefore} 元<br/>`;
        result += `<strong>节省: ${optimizedBefore - optimizedAfter} 元</strong>`;
        return result;
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#3b82f6',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
      },
    },
    xAxis: {
      axisTick: { show: false },
      data: dateData,
      type: 'category',
      axisLine: {
        lineStyle: {
          color: '#ffffff',
          width: 1,
        },
      },
      axisLabel: {
        color: '#ffffff',
        fontSize: 11,
      },
    },
    yAxis: {
      axisTick: { show: false },
      type: 'value',
      axisLine: { show: false },
      axisLabel: {
        color: '#ffffff',
        fontSize: 11,
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed',
        },
      },
      min: 0,
      max: 5000,
    },
    series: [
      // 优化后费用（底层）
      {
        z: 1,
        name: '优化后',
        type: 'bar',
        stack: 'cost',
        barWidth,
        color: colors[0],
        data: mockData.optimizedAfter,
        // animationDuration: 1500,
        // animationEasing: 'cubicOut',
        // animationDelay: (idx) => idx * 100,
      },
      // 节省的费用（上层，显示为优化前的颜色）
      {
        z: 2,
        name: '优化前',
        type: 'bar',
        stack: 'cost',
        barWidth,
        color: colors[1],
        data: mockData.optimizedSavings,
        // animationDuration: 1500,
        // animationEasing: 'cubicOut',
        // animationDelay: (idx) => idx * 100,
      },
      // 柱形底部菱形
      {
        z: 3,
        name: '优化后',
        type: 'pictorialBar',
        data: constData,
        symbol: 'diamond',
        symbolOffset: ['0%', '50%'],
        symbolSize: [diamondWidth, diamondHeight],
        tooltip: { show: false },
        legendHoverLink: false,
        // animationDuration: 1500,
        // animationEasing: 'cubicOut',
        // animationDelay: (idx) => idx * 100,
      },
      // 中间分界菱形（在优化后费用的顶部）- 橙色柱体的底部
      {
        z: 4,
        name: '优化前',
        type: 'pictorialBar',
        data: mockData.optimizedAfter,
        symbol: 'diamond',
        symbolPosition: 'end',
        symbolOffset: ['0%', '-50%'],
        symbolSize: [diamondWidth, diamondHeight],
        itemStyle: {
          color: colors[1], // 使用与优化前柱体相同的橙色线性渐变
        },
        tooltip: { show: false },
        legendHoverLink: false,
        // animationDuration: 1500,
        // animationEasing: 'cubicOut',
        // animationDelay: (idx) => idx * 100,
      },
      // 柱形顶部菱形
      {
        z: 5,
        name: '优化前',
        type: 'pictorialBar',
        symbolPosition: 'end',
        data: topData,
        symbol: 'diamond',
        symbolOffset: ['0%', '-50%'],
        symbolSize: [diamondWidth, diamondHeight],
        tooltip: { show: false },
        legendHoverLink: false,
        // animationDuration: 1500,
        // animationEasing: 'cubicOut',
        // animationDelay: (idx) => idx * 100,
        label: {
          formatter: '{c}',
          show: true,
          position: 'top',
          fontWeight: 400,
          fontSize: 12,
          color: '#ffffff',
          lineHeight: 16,
          // animationDuration: 800,
          // animationDelay: (idx) => idx * 100 + 700,
        },
      },
    ],
  });

  // 获取图表实例
  const chartInstance = getChartInstance();

  // 监听窗口大小变化，自动调整图表大小
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  window.addEventListener('resize', handleResize);

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  // 注意：图例已禁用点击切换功能（selectedMode: false），
  // 以避免3D柱体消失时的动画不同步问题
});
</script>

<template>
  <div class="seven-day-cost-chart relative flex h-full w-full flex-col">
    <EchartsUI ref="chartRef" class="flex-1" width="100%" :height="chartHeight" />
  </div>
</template>

<style scoped>
.seven-day-cost-chart {
  background: transparent;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 确保 EchartsUI 组件完全填充容器 */
.seven-day-cost-chart :deep(div) {
  height: 100% !important;
}
</style>
