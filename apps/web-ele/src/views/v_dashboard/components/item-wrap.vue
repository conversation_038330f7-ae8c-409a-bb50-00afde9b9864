<script setup lang="ts">
import { computed } from 'vue';

import { BorderBox8 as DvBorderBox } from '@kjgl77/datav-vue3';

import title0bg from '../assets/<EMAIL>';
import title1bg from '../assets/<EMAIL>';
import title2bg from '../assets/<EMAIL>';
import title3bg from '../assets/<EMAIL>';
import title4bg from '../assets/<EMAIL>';
import title5bg from '../assets/<EMAIL>';
import title6bg from '../assets/<EMAIL>';

const props = withDefaults(
  defineProps<{
    order: number | string;
    // 标题
    title?: number | string;
  }>(),
  {
    title: '',
    order: 0,
  },
);

// 创建图片映射对象，支持动态访问
const titleBgMap: Record<string, string> = {
  '0': title0bg,
  '1': title1bg,
  '2': title2bg,
  '3': title3bg,
  '4': title4bg,
  '5': title5bg,
  '6': title6bg, // 保持原有逻辑，order为6时使用title5bg
};

const usedBg = computed(() => {
  const orderKey = String(props.order);
  return titleBgMap[orderKey] || ''; // 如果找不到对应的图片，返回空字符串
});
</script>

<template>
  <div class="block">
    <DvBorderBox :dur="10">
      <div class="flex h-full w-full flex-col overflow-hidden">
        <!--  标题  -->
        <div
          class="h-[50px] w-full shrink-0 overflow-hidden bg-cover bg-fixed bg-clip-content bg-no-repeat"
          :style="{
            backgroundImage: `url(${usedBg})`,
            // backgroundSize: '100%% 100%',
            backgroundPosition: '1px 2px',
          }"
        ></div>
        <div class="flex-1 bg-origin-content p-[2px]">
          <slot></slot>
        </div>
      </div>
    </DvBorderBox>
  </div>
</template>

<style scoped lang="scss"></style>
