<script setup lang="ts">
import type { DashboardField } from '../types/dashboard';

import icon from '../assets/<EMAIL>';

interface Props {
  fields: DashboardField[];
}

const props = defineProps<Props>();

// 根据布局需求分组字段
// 上面一行：前3个字段（电压等级、变压器容量、光伏容量）
// 下面一行：后4个字段，分为两行两列
const topRowFields = props.fields.slice(0, 3);
const bottomRowFields = props.fields.slice(3, 7);
</script>

<template>
  <div class="project-basic-info flex h-full w-full flex-col">
    <!-- 上面一行：三列布局 - 精确尺寸控制 -->
    <div
      class="top-row mx-5 mb-[30px] mt-[10px] grid juctify-center h-[110px] grid-cols-3 gap-x-[20px]"
    >
      <div
        v-for="field in topRowFields"
        :key="field.key"
        class="info-card h-full w-[100px]"
      >
        <!-- 背景图占位元素 -->
        <div class="absolute inset-0"></div>

        <!-- 内容区域 - 两行文字布局 -->
        <div
          class="relative z-10 flex h-full flex-col items-center justify-center"
          :style="{
            background: `url(${icon}) no-repeat center center`,
            backgroundSize: 'cover',
          }"
        >
          <!-- 第一行：数值 -->
          <div class="value mb-1 text-lg font-bold text-[#FF9B3F]">
            {{ field.value }}
          </div>
          <!-- 第二行：标签 -->
          <div class="label text-xs text-blue-100">
            {{ field.label }}
          </div>
        </div>
      </div>
    </div>

    <!-- 下面一行：两行两列布局 - 占用剩余空间 -->
    <div class="bottom-section mx-10 mb-[14px] grid flex-1 grid-cols-2 gap-2">
      <!-- 左侧两个字段 -->
      <div class="left-column flex flex-col gap-1">
        <div
          v-for="field in bottomRowFields.slice(0, 2)"
          :key="field.key"
          class="info-card flex flex-1 items-center rounded-lg bg-gradient-to-r from-blue-500/20 to-blue-600/30 p-2"
        >
          <!-- 占位图标盒子 -->
          <div
            class="icon-placeholder mr-2 h-6 w-6 rounded-lg bg-blue-400/50"
          ></div>
          <div class="flex-1">
            <!-- 标签 -->
            <div class="label mb-1 text-xs text-blue-200">
              {{ field.label }}
            </div>
            <!-- 数值 -->
            <div class="value text-sm font-bold text-orange-400">
              {{ field.value }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧两个字段 -->
      <div class="right-column flex flex-col gap-1">
        <div
          v-for="field in bottomRowFields.slice(2, 4)"
          :key="field.key"
          class="info-card flex flex-1 items-center rounded-lg bg-gradient-to-r from-blue-500/20 to-blue-600/30 p-2"
        >
          <!-- 占位图标盒子 -->
          <div
            class="icon-placeholder mr-2 h-6 w-6 rounded-lg bg-blue-400/50"
          ></div>
          <div class="flex-1">
            <!-- 标签 -->
            <div class="label mb-1 text-xs text-blue-200">
              {{ field.label }}
            </div>
            <!-- 数值 -->
            <div class="value text-sm font-bold text-orange-400">
              {{ field.value }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.project-basic-info {
  .info-card {
  }
}
</style>
