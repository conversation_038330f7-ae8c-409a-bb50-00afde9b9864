<script setup lang="ts">
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { DashboardField } from '../types/dashboard';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

interface Props {
  fields: DashboardField[];
}

const props = defineProps<Props>();

// 根据布局需求分组字段
// 上面一行：年用电量、月用电量、日用电量、当月需量（2行2列卡片）
// 下面一行：电网月供电、电网年供电、新能源月消纳、新能源年消纳（横轴柱状图）
const topRowFields = props.fields.slice(0, 4); // 前4个字段（卡片显示）
const bottomRowFields = props.fields.slice(4, 8); // 后4个字段（柱状图显示）

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 生成柱状图数据
const generateChartData = () => {
  return bottomRowFields.map((field) => ({
    name: field.label,
    value: Number.parseFloat(field.value?.replace(/[^\d.]/g, '') || '0'), // 提取数值
    unit: field.value?.replace(/[\d.]/g, '') || 'MWh', // 提取单位
  }));
};

// 柱状图颜色配置 - 参考demo.js的四种颜色
const getBarColors = () => {
  return [
    // 第一种颜色 - 红色渐变
    {
      type: 'linear',
      x: 0,
      y: 1,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: 'rgba(254, 69, 97, 0)' },
        { offset: 1, color: 'rgba(254, 69, 97, 1)' },
      ],
    },
    // 第二种颜色 - 橙色渐变
    {
      type: 'linear',
      x: 0,
      y: 1,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: 'rgba(254, 69, 97, 0)' },
        { offset: 1, color: 'rgba(255, 155, 0, 1)' },
      ],
    },
    // 第三种颜色 - 黄色渐变
    {
      type: 'linear',
      x: 0,
      y: 1,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: 'rgba(254, 69, 97, 0)' },
        { offset: 1, color: 'rgba(233, 217, 0, 1)' },
      ],
    },
    // 第四种颜色 - 青色渐变
    {
      type: 'linear',
      x: 0,
      y: 1,
      x2: 1,
      y2: 1,
      colorStops: [
        { offset: 0, color: 'rgba(0, 245, 255, 0)' },
        { offset: 1, color: 'rgba(0, 245, 255, 1)' },
      ],
    },
  ];
};

onMounted(async () => {
  const chartData = generateChartData();
  const colors = getBarColors();

  await renderEcharts({
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        for (const param of params) {
          return `${param.name}: ${param.data.value} MWh`;
        }
      },
    },
    grid: {
      containLabel: true,
      bottom: '5%',
      left: '-15%',
      top: '10%',
      right: '2%',
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: [
      // 第一个Y轴 - 左侧标签（隐藏，用于数据映射）
      {
        type: 'category',
        data: chartData.map((item) => item.name),
        inverse: true,
        position: 'left',
        axisLabel: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      // 第二个Y轴 - 右侧数值
      {
        type: 'category',
        data: chartData.map((item) => item.value),
        inverse: true,
        position: 'right',
        axisLabel: {
          padding: [-3, 0, 0, 0],
          margin: 10,
          formatter(value: any, index: number) {
            return `{a|${chartData[index].value}}{b|MWh}`;
          },
          rich: {
            a: {
              fontSize: 15,
              color: '#fff',
              padding: [4, 5, 0, 0],
            },
            b: {
              padding: [4, 0, 0, 0],
              color: '#fff',
            },
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      // 第三个Y轴 - 左侧显示的标签
      {
        type: 'category',
        inverse: true,
        position: 'left',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        data: chartData.map((item) => item.name),
        axisLabel: {
          show: true,
          margin: -2,
          fontSize: 12,
          align: 'top',
          verticalAlign: 'bottom',
          padding: [0, 0, 10, 0],
          color: '#fff',
        },
      },
    ],
    series: [
      {
        data: chartData.map((item, i) => {
          const itemStyle = {
            color: colors[i],
          };
          return {
            value: item.value,
            itemStyle,
          };
        }),
        type: 'bar',
        barWidth: 6,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(32, 104, 118, 1)',
          borderRadius: 30,
        },
      },
    ],
  });
});
</script>

<template>
  <div class="electricity-usage-analysis flex h-full w-full flex-col">
    <!-- 上半部分：2行2列卡片布局 - 显示前4个字段，等分空间 -->
    <div
      class="top-section grid h-[24.93%] grid-cols-2 grid-rows-2 gap-2 mx-[10px] mt-[20px] mb-[16px]"
    >
      <div
        v-for="field in topRowFields"
        :key="field.key"
        class="info-card flex items-center rounded-lg bg-gradient-to-b from-blue-500/20 to-blue-600/30 p-2"
      >
        <!-- 左侧图标 -->
        <div class="icon-section flex w-1/3 items-center justify-center">
          <div class="icon-placeholder h-8 w-8 rounded-lg bg-blue-400/50"></div>
        </div>
        <!-- 右侧两行文字 -->
        <div class="content-section ml-2 flex flex-1 flex-col justify-center">
          <!-- 第一行：标签 -->
          <div class="label mb-1 text-xs text-blue-100">
            {{ field.label }}
          </div>
          <!-- 第二行：数值 -->
          <div class="value text-sm font-bold text-orange-400">
            {{ field.value }}
          </div>
        </div>
      </div>
    </div>

    <!-- 下半部分：横轴柱状图 - 显示后4个字段，自适应剩余高度 -->
    <div class="bottom-section flex-1">
      <EchartsUI ref="chartRef" height="100%" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.electricity-usage-analysis {
  .top-section {
    // 等分网格布局
    grid-template-rows: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
  }

  .info-card {
    border: 1px solid rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.5);
    }
  }

  .icon-placeholder {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.6),
      rgba(37, 99, 235, 0.8)
    );
    border: 1px solid rgba(59, 130, 246, 0.4);
  }

  .icon-section {
    flex-shrink: 0; // 防止图标区域被压缩
  }

  .content-section {
    min-width: 0; // 允许文字区域收缩
  }

  .value {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .label {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .bottom-section {
    //background: rgba(0, 0, 0, 0.1);
    //border-radius: 8px;
    //border: 1px solid rgba(59, 130, 246, 0.2);
  }
}
</style>
