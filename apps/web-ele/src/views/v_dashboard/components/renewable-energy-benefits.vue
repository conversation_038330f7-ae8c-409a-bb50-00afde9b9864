<script setup lang="ts">
import type { DashboardField } from '../types/dashboard';

interface Props {
  fields: DashboardField[];
}

const props = defineProps<Props>();

// 根据布局需求分组字段
// 上面两行两列：年发电量、年有效小时数(嵌套)、年自用电量、年上网电量
// 下面一行四列：减少碳排放、节约标准煤、等效植树数、等效绿证数
const topRowFields = props.fields.slice(0, 4); // 前4个字段
const bottomRowFields = props.fields.slice(4, 8); // 后4个字段

// 处理年有效小时数的嵌套字段
const getNestedFields = (field: DashboardField) => {
  return field.fields || [];
};


</script>

<template>
  <div class="renewable-energy-benefits flex h-full w-full flex-col">
    <!-- 上面两行两列布局 - 精确尺寸控制 -->
    <div class="top-section grid h-[49.4%] grid-cols-2 grid-rows-2 gap-2 mx-[10px] mt-[20px] mb-[16px]">
      <template v-for="(field, index) in topRowFields" :key="field.key">
        <!-- 年有效小时数特殊处理（有嵌套字段） -->
        <div
          v-if="field.key === 'annualEffectiveHours'"
          class="info-card flex rounded-lg bg-gradient-to-b from-blue-500/20 to-blue-600/30 p-2"
        >
          <!-- 左侧图标 -->
          <div class="icon-section flex items-center justify-center w-1/3">
            <div class="icon-placeholder h-8 w-8 rounded-lg bg-blue-400/50"></div>
          </div>
          <!-- 右侧内容 -->
          <div class="content-section flex-1 flex flex-col justify-center">
            <!-- 主标题 -->
            <div class="mb-2 text-xs text-blue-200">
              {{ field.label }}
            </div>
            <!-- 嵌套字段：光伏和风力 - 上下两行显示 -->
            <div class="flex flex-col gap-1">
              <div
                v-for="nestedField in getNestedFields(field)"
                :key="nestedField.key"
                class="flex items-center gap-2"
              >
                <!-- 子标签 -->
                <div class="label text-xs text-blue-100">
                  {{ nestedField.label }}
                </div>
                <!-- 数值 -->
                <div class="value text-xs font-bold text-orange-400">
                  {{ nestedField.value }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他普通字段 -->
        <div
          v-else
          class="info-card flex items-center rounded-lg bg-gradient-to-b from-blue-500/20 to-blue-600/30 p-2"
        >
          <!-- 左侧图标 -->
          <div class="icon-section flex items-center justify-center w-1/3">
            <div class="icon-placeholder h-8 w-8 rounded-lg bg-blue-400/50"></div>
          </div>
          <!-- 右侧两行文字 -->
          <div class="content-section flex-1 flex flex-col justify-center">
            <!-- 第一行：标签 -->
            <div class="label mb-1 text-xs text-blue-100">
              {{ field.label }}
            </div>
            <!-- 第二行：数值 -->
            <div class="value text-sm font-bold text-orange-400">
              {{ field.value }}
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 下面一行四列布局 - 占用剩余空间 -->
    <div class="bottom-section flex-1 grid grid-cols-4 gap-2 mx-[10px] mb-[20px]">
      <div
        v-for="field in bottomRowFields"
        :key="field.key"
        class="info-card flex flex-col rounded-lg bg-gradient-to-b from-blue-500/20 to-blue-600/30 p-2"
      >
        <!-- 标签在上 -->
        <div class="label mb-[5px] text-xs text-blue-100 text-center">
          {{ field.label }}
        </div>
        <!-- 图表占位盒子 -->
        <div class="chart-placeholder flex-1 flex items-center justify-center rounded border border-blue-400/30">
          <!-- 数值显示在占位盒子中心 -->
          <div class="value text-sm font-bold text-orange-400">
            {{ field.value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>



<style scoped lang="scss">
.renewable-energy-benefits {
  .info-card {
    border: 1px solid rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
      border-color: rgba(59, 130, 246, 0.5);
    }
  }

  .icon-placeholder {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(37, 99, 235, 0.8));
    border: 1px solid rgba(59, 130, 246, 0.4);
  }

  .icon-section {
    flex-shrink: 0; // 防止图标区域被压缩
  }

  .content-section {
    min-width: 0; // 允许文字区域收缩
  }

  .chart-placeholder {
    background: transparent; // 去掉背景
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(59, 130, 246, 0.6);
      background: rgba(59, 130, 246, 0.05);
    }
  }

  .value {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .label {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}
</style>
