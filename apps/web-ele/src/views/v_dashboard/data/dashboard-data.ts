import type { DashboardDataSet } from '../types/dashboard';

/**
 * 仪表板数据集
 */
export const dashboardDataSet: DashboardDataSet = [
  {
    title: '项目基本信息',
    order: 0,
    fields: [
      {
        label: '电压等级',
        key: 'voltageLevel',
        value: '0.4kV',
      },
      {
        label: '变压器容量',
        key: 'transformerCapacity',
        value: '500kvA',
      },
      {
        label: '光伏容量',
        key: 'solarCapacity',
        value: '300kw',
      },
      {
        label: '风电容量',
        key: 'windCapacity',
        value: '5km',
      },
      {
        label: '储能容量',
        key: 'storageCapacity',
        value: '60/107kw',
      },
      {
        label: '直流充电桩',
        key: 'chargingPileCapacity',
        value: '5km',
      },
      {
        label: '交流电充电桩',
        key: 'acChargingPileCapacity',
        value: '60/107kw',
      },
    ],
  },
  {
    title: '当前能耗排名',
    order: 1,
    fileds: [
      {
        label: '排名',
        key: 'rank',
      },
      {
        label: '用电支路',
        key: 'electricalCircuit',
      },
      {
        label: '实时功率',
        key: 'realTimePower',
      },
      {
        label: '累计用电量',
        key: 'cumulativeElectricityConsumption',
      },
    ],
  },
  {
    title: '新能源社会效益',
    order: 2,
    fields: [
      {
        label: '年发电量',
        key: 'annualGeneration',
        value: '172.6MW.h',
      },
      {
        label: '年有效小时数',
        key: 'annualEffectiveHours',
        fields: [
          {
            label: '光伏',
            key: 'solar',
            value: '1000h',
          },
          {
            label: '风力',
            key: 'wind',
            value: '1000h',
          },
        ],
      },
      {
        label: '年自用电量',
        key: 'annualSelfConsumption',
        value: '154.8MW.h',
      },
      {
        label: '年上网电量',
        key: 'annualGridConnection',
        value: '154.8MW.h',
      },
      {
        label: '减少碳排放',
        key: 'reducedCarbonEmission',
        value: '300.3t',
      },
      {
        label: '节约标准煤',
        key: 'savedStandardCoal',
        value: '300.3t',
      },
      {
        label: '等效植树数',
        key: 'equivalentPlanting',
        value: '300颗',
      },
      {
        label: '等效绿证数',
        key: 'equivalentGreenCertificate',
        value: '300张',
      },
    ],
  },
  {
    title: '近七日费用对比',
    order: 3,
    fields: [
      {
        label: '优化前',
        key: 'beforeOptimization',
      },
      {
        label: '优化后',
        key: 'afterOptimization',
      },
    ],
  },
  {
    title: '用电情况分析',
    order: 4,
    fields: [
      {
        label: '年用电量',
        key: 'annualElectricityConsumption',
        value: '172.6MW.h',
      },
      {
        label: '月用电量',
        key: 'monthlyElectricityConsumption',
        value: '172.6MW.h',
      },
      {
        label: '日用电量',
        key: 'dailyElectricityConsumption',
        value: '172.6MW.h',
      },
      {
        label: '当月需量',
        key: 'monthlyDemand',
        value: '172.6MW.h',
      },
      {
        label: '电网月供电',
        key: 'gridSupplyOfMonth',
        value: '172.6MW.h',
      },
      {
        label: '电网年供电',
        key: 'gridSupplyOfYear',
        value: '172.6MW.h',
      },
      {
        label: '新能源月消纳',
        key: 'newEnergyConsumptionOfMonth',
        value: '172.6MW.h',
      },
      {
        label: '新能源年消纳',
        key: 'newEnergyConsumptionOfYear',
        value: '172.6MW.h',
      },
    ],
  },
  {
    title: '运行功率曲线',
    order: 5,
    fields: [
      {
        label: '负荷',
        key: 'load',
      },
      {
        label: '储能',
        key: 'storage',
      },
      {
        label: '市电',
        key: 'grid',
      },
      {
        label: '新能源',
        key: 'newEnergy',
      },
    ],
  },
  {
    title: '电力需求曲线',
    order: 6,
  },
  {
    title: '设备运行状态',
    order: -1,
    fields: [
      {
        label: '累计节约电量',
        key: 'cumulativeEnergySaving',
        value: '172.6MW.h',
      },
      {
        label: '累计运行天数',
        key: 'cumulativeRunningDays',
        value: '172天',
      },
      {
        label: '累计系统收益',
        key: 'cumulativeSystemIncome',
        value: '172万元',
      },
      {
        label: '投资回收期',
        key: 'investmentRecoveryPeriod',
        value: '5.4年',
      },
      {
        label: '倒计时',
        key: 'countdown',
        value: '321天',
      },
      {
        label: '光伏',
        key: 'solar',
        fields: [
          {
            label: '日发电量',
            key: 'dailyGeneration',
            value: '32MWh',
          },
          {
            label: '日消纳量',
            key: 'dailyConsumption',
            value: '32MWh',
          },
        ],
      },
      {
        label: '风电',
        key: 'wind',
        fields: [
          {
            label: '日发电量',
            key: 'dailyGeneration',
            value: '32MWh',
          },
          {
            label: '日收益',
            key: 'dailyIncome',
            value: '32MWh',
          },
        ],
      },
      {
        label: '市电',
        key: 'grid',
        fields: [
          {
            label: '上网电量',
            key: 'dailyGeneration',
            value: '32MWh',
          },
          {
            label: '上网收益',
            key: 'dailyIncome',
            value: '32MWh',
          },
          {
            label: '下网电量',
            key: 'dailyConsumption',
            value: '32MWh',
          },
        ],
      },
      {
        label: '储能',
        key: 'storage',
        fields: [
          {
            label: '充放电功率',
            key: 'chargingDischargingPower',
            value: '32MWh',
          },
          {
            label: '储能SCO',
            key: 'storageSCO',
            value: '32MWh',
          },
        ],
      },
      {
        label: '负荷',
        key: 'load',
        fields: [
          {
            label: '日用电量',
            key: 'dailyElectricityConsumption',
            value: '32MWh',
          },
        ],
      },
      {
        label: '充电桩',
        key: 'chargingPile',
        fields: [
          {
            label: '日充电量',
            key: 'dailyCharging',
            value: '32MWh',
          },
          {
            label: '日充电收益',
            key: 'dailyIncome',
            value: '32MWh',
          },
        ],
      },
      {
        label: '峰谷套利',
        key: 'peakValleyArbitrage',
        fields: [
          {
            label: '当月节约电费',
            key: 'monthlyElectricitySaving',
            value: '5000',
          },
          {
            label: '累计节约电费',
            key: 'cumulativeElectricitySaving',
            value: '320000',
          },
        ],
      },
      {
        label: '需量管理',
        key: 'demandManagement',
        fields: [
          {
            label: '当月降低需量',
            key: 'monthlyReducedDemand',
            value: '50kW',
          },
          {
            label: '当月节约电费',
            key: 'monthlyElectricitySaving',
            value: '320000',
          },
          {
            label: '累计节约电费',
            key: 'cumulativeElectricitySaving',
            value: '320000',
          },
        ],
      },
      {
        label: '新能源消纳',
        key: 'newEnergyConsumption',
        fields: [
          {
            label: '当月消纳电量',
            key: 'monthlyConsumptionAmount',
            value: '32MWh',
          },
          {
            label: '累计节约电费',
            key: 'cumulativeConsumptionAmount',
            value: '43.9',
          },
        ],
      },
      {
        label: '综合用电对比',
        key: 'comprehensiveElectricityComparison',
        fields: [
          {
            label: '投入前',
            key: 'beforeInvestment',
            value: '0.75 元/kW·h',
          },
          {
            label: '投入后',
            key: 'afterInvestment',
            value: '0.75 元/kW·h',
          },
        ],
      },
    ],
  },
];
