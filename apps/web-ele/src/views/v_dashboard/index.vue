<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue';

import autofit from 'autofit.js';

import ElectricityUsageAnalysis from './components/electricity-usage-analysis.vue';
import Header from './components/header.vue';
import ItemWrap from './components/item-wrap.vue';
import OperationPowerCurveChart from './components/operation-power-curve-chart.vue';
import ProjectBasicInfo from './components/project-basic-info.vue';
import RenewableEnergyBenefits from './components/renewable-energy-benefits.vue';
import SevenDayCostComparisonChart from './components/seven-day-cost-comparison-chart.vue';
import { dashboardDataSet } from './data';

const dataSet = ref(dashboardDataSet);

// 获取项目基本信息的字段数据
const projectBasicInfoFields = computed(() => {
  const projectInfo = dataSet.value.find((item) => item.order === 0);
  return projectInfo?.fields || [];
});

// 获取新能源社会效益的字段数据
const renewableEnergyBenefitsFields = computed(() => {
  const renewableInfo = dataSet.value.find((item) => item.order === 2);
  return renewableInfo?.fields || [];
});

// 获取用电情况分析的字段数据
const electricityUsageAnalysisFields = computed(() => {
  const electricityInfo = dataSet.value.find((item) => item.order === 4);
  return electricityInfo?.fields || [];
});

onMounted(() => {
  autofit.init({
    dh: 1080,
    dw: 1920,
    el: '#root-visualizer-container',
    resize: true,
  });
});

onUnmounted(() => {
  // 清理 autofit 实例
  autofit.off();
});
</script>

<template>
  <div id="root-visualizer-container">
    <div id="visualizer-container" class="relative h-full w-full">
      <Header />
      <!-- 大屏主内容区 - 三列布局 -->
      <div class="min-h-[calc(100% - 101px)] mx-[30px] mb-[28px] flex flex-1">
        <!-- 左侧区域 -->
        <div class="dashboard-left-panel">
          <!-- 项目基本信息 -->
          <ItemWrap :order="0" class="box-item mb-[20px] w-full">
            <ProjectBasicInfo :fields="projectBasicInfoFields" />
          </ItemWrap>

          <!-- 当前能耗排名 -->
          <ItemWrap :order="1" class="box-item mb-[20px] w-full" />

          <!-- 新能源社会效益 -->
          <ItemWrap :order="2" class="box-item w-full">
            <RenewableEnergyBenefits :fields="renewableEnergyBenefitsFields" />
          </ItemWrap>
        </div>

        <!-- 中间区域 -->
        <div
          class="dashboard-center-panel flex flex-1 flex-col justify-between"
        >
          <!-- 主仪表盘区域 -->
          <div class="flex-1"></div>
          <!-- 电力需求曲线 -->
          <ItemWrap :order="6" class="w-ull h-1/3" />
        </div>

        <!-- 右侧区域 -->
        <div class="dashboard-right-panel">
          <!-- 运行功率曲线 -->
          <ItemWrap :order="3" class="box-item mb-[20px] w-full">
            <OperationPowerCurveChart />
          </ItemWrap>

          <!-- 近七日费用对比 -->
          <ItemWrap :order="4" class="box-item mb-[20px] w-full">
            <SevenDayCostComparisonChart />
          </ItemWrap>

          <!-- 用电情况分析 -->
          <ItemWrap :order="5" class="box-item w-full">
            <ElectricityUsageAnalysis
              :fields="electricityUsageAnalysisFields"
            />
          </ItemWrap>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
#root-visualizer-container {
  background-color: #000d86;
  position: relative;
  height: 100vh;
}

#visualizer-container {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  flex-direction: column;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top left;
  background-attachment: fixed;
}

// 左侧面板样式
.dashboard-left-panel {
  @apply relative box-border h-full w-1/5;
}

// 中间面板样式
.dashboard-center-panel {
  @apply mx-[14px];
}

// 右侧面板样式
.dashboard-right-panel {
  @apply relative box-border h-full w-1/5;
}
.box-item:not(:last-child) {
  height: calc(33.33% - 20px);
}
.box-item:last-child {
  height: calc(33.33% - 0px);
}
</style>
