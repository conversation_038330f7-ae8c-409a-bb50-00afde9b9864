/**
 * 仪表板数据字段接口
 */
export interface DashboardField {
  /** 字段标签 */
  label: string;
  /** 字段键名 */
  key: string;
  /** 字段值 */
  value?: string;
  /** 嵌套字段 */
  fields?: DashboardField[];
}

/**
 * 仪表板数据项接口
 */
export interface DashboardItem {
  /** 标题 */
  title: string;
  /** 排序顺序 */
  order: number;
  /** 字段列表 */
  fields?: DashboardField[];
  /** 字段列表（兼容拼写错误） */
  fileds?: DashboardField[];
}

/**
 * 仪表板数据集类型
 */
export type DashboardDataSet = DashboardItem[];
