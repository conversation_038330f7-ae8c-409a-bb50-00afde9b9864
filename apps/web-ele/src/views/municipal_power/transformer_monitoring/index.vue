<template>
  <div class="h-full flex flex-col">
    <!-- 第一行：高度45px，5px padding，5px margin-bottom -->
    <div class="h-[45px] p-[5px] mb-[5px] bg-white rounded-lg shadow flex items-center">
      <h1 class="text-lg font-bold">变压器监测</h1>
      <span class="ml-2 text-sm text-gray-500">Transformer Monitoring</span>
    </div>

    <!-- 第二行：50%高度，5px margin-bottom，内部两列布局 -->
    <div class="h-[50%] mb-[5px] flex">
      <!-- 左列：25%宽度，5px margin-right -->
      <div class="w-[25%] mr-[5px] bg-white rounded-lg shadow p-4">
        <div class="h-full flex flex-col">
          <h2 class="text-base font-semibold mb-3">监测参数</h2>
          <div class="flex-1 space-y-2">
            <div class="p-2 bg-gray-50 rounded">
              <div class="text-sm text-gray-600">电压</div>
              <div class="text-lg font-bold text-blue-600">220V</div>
            </div>
            <div class="p-2 bg-gray-50 rounded">
              <div class="text-sm text-gray-600">电流</div>
              <div class="text-lg font-bold text-green-600">15A</div>
            </div>
            <div class="p-2 bg-gray-50 rounded">
              <div class="text-sm text-gray-600">功率</div>
              <div class="text-lg font-bold text-orange-600">3.3kW</div>
            </div>
            <div class="p-2 bg-gray-50 rounded">
              <div class="text-sm text-gray-600">温度</div>
              <div class="text-lg font-bold text-red-600">45°C</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右列：75%-5px宽度 -->
      <div class="flex-1 bg-white rounded-lg shadow p-4">
        <div class="h-full flex flex-col">
          <h2 class="text-base font-semibold mb-3">实时监控图表</h2>
          <div class="flex-1 flex items-center justify-center bg-gray-50 rounded">
            <div class="text-center">
              <div class="text-4xl mb-2">📊</div>
              <p class="text-gray-500">图表区域</p>
              <p class="text-sm text-gray-400">实时数据图表将在此显示</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第三行：剩余高度自适应 -->
    <div class="flex-1 bg-white rounded-lg shadow p-4">
      <div class="h-full flex flex-col">
        <h2 class="text-base font-semibold mb-3">历史数据与报警信息</h2>
        <div class="flex-1 bg-gray-50 rounded p-4">
          <div class="grid grid-cols-2 gap-4 h-full">
            <div class="bg-white rounded p-3">
              <h3 class="text-sm font-medium mb-2">历史趋势</h3>
              <div class="h-full flex items-center justify-center">
                <div class="text-center">
                  <div class="text-2xl mb-1">📈</div>
                  <p class="text-xs text-gray-500">历史数据趋势图</p>
                </div>
              </div>
            </div>
            <div class="bg-white rounded p-3">
              <h3 class="text-sm font-medium mb-2">报警记录</h3>
              <div class="space-y-1 text-xs">
                <div class="p-1 bg-red-50 text-red-600 rounded">高温报警 - 2024-01-20 14:30</div>
                <div class="p-1 bg-yellow-50 text-yellow-600 rounded">电压异常 - 2024-01-20 12:15</div>
                <div class="p-1 bg-green-50 text-green-600 rounded">系统正常 - 2024-01-20 10:00</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'TransformerMonitoring',
});
</script>
