<script lang="ts" setup>
import type { JsonViewerAction, JsonViewerValue } from '@vben/common-ui';

import { JsonViewer, Page } from '@vben/common-ui';

import { ElCard, ElMessage } from 'element-plus';

import { json1, json2 } from './data';

function handleKeyClick(key: string) {
  ElMessage.info(`点击了Key ${key}`);
}

function handleValueClick(value: JsonViewerValue) {
  ElMessage.info(`点击了Value ${JSON.stringify(value)}`);
}

function handleCopied(_event: JsonViewerAction) {
  ElMessage.success('已复制JSON');
}
</script>
<template>
  <Page
    title="Json Viewer"
    description="一个渲染 JSON 结构数据的组件，支持复制、展开等，简单易用"
  >
    <ElCard>
      <template #header>默认配置</template>
      <JsonViewer :value="json1" />
    </ElCard>
    <ElCard class="mt-4">
      <template #header>可复制、默认展开3层、显示边框、事件处理</template>
      <JsonViewer
        :value="json2"
        :expand-depth="3"
        copyable
        :sort="false"
        @key-click="handleKeyClick"
        @value-click="handleValueClick"
        @copied="handleCopied"
        boxed
      />
    </ElCard>
    <ElCard class="mt-4">
      <template #header>预览模式</template>
      <JsonViewer
        :value="json2"
        copyable
        preview-mode
        :show-array-index="false"
      />
    </ElCard>
  </Page>
</template>
