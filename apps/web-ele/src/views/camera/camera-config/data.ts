import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CameraApi, CameraConfigData } from '#/api/camera';
import { $t } from "@vben/locales";

/*  用于编辑新增表单  */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'camera_name',
      label: '摄像头名称',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'brand',
      label: '品牌',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'serial_number',
      label: '序列号',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'video_token',
      label: '视频Token',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'video_key',
      label: '视频Key',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'video_secret',
      label: '视频Secret',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'region',
      label: '所在区域',
      rules: 'required',
    },
  ];
}

/*  用于表格搜索表单  */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        class:
          'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm h-[40px]',
        placeholder: '请输入摄像头名称',
      },
      // 字段名
      fieldName: 'camera_name',
      // 界面显示的label
      label: '摄像头名称',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        class:
          'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm h-[40px]',
        placeholder: '请输入品牌',
      },
      // 字段名
      fieldName: 'brand',
      // 界面显示的label
      label: '品牌',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        class:
          'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm h-[40px]',
        placeholder: '请输入序列号',
      },
      // 字段名
      fieldName: 'serial_number',
      // 界面显示的label
      label: '序列号',
    },
  ];
}

export function useColumns<T = CameraApi.CameraConfig>(
  onActionClick?: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions<CameraConfigData>['columns'] {
  return [
    {
      align: 'center',
      field: 'camera_name',
      fixed: 'left',
      title: '摄像头名称',
      minWidth: 150, // 使用 minWidth 替代固定 width，让表格自适应
    },
    {
      align: 'center',
      field: 'brand',
      title: '品牌',
      minWidth: 120,
    },
    {
      align: 'center',
      field: 'serial_number',
      title: '序列号',
      minWidth: 180,
    },
    {
      align: 'center',
      field: 'video_token',
      title: '视频Token',
      minWidth: 200,
    },
    {
      align: 'center',
      field: 'video_key',
      title: '视频Key',
      minWidth: 150,
    },
    {
      align: 'center',
      field: 'video_secret',
      title: '视频Secret',
      minWidth: 200,
    },
    {
      align: 'center',
      field: 'region',
      title: '所在区域',
      minWidth: 120,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'camera_name',
          nameTitle: $t('camera.cameraConfig.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('camera.cameraConfig.operation'),
      width: 130,
    },
  ];
}
