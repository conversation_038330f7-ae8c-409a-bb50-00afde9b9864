<script lang="ts" setup>
import type { Recordable } from '@vben-core/typings';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { CameraApi, VideoItemData, VideoListResponse } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { $t } from '@vben/locales';

import { ElButton, ElMessage, ElMessageBox } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteCamera, getCameraList, updateCamera } from '#/api';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    // 默认展开搜索表单
    collapsed: false,
    // 所有表单项共用配置
    commonConfig: {
      // 所有表单项统一样式
      componentProps: {
        class: 'w-full',
      },
    },
    schema: useGridFormSchema(),
    // 控制表单是否显示折叠按钮
    showCollapseButton: false,
    // 是否在字段值改变时提交表单
    submitOnChange: false,
    // 按下回车时是否提交表单
    submitOnEnter: true,
    // 表单布局
    layout: 'horizontal',
  },
  showSearchForm: true,
  gridOptions: {
    ...createGridOptions(),
    proxyConfig: {
      autoLoad: true, // 启用自动加载，页面加载时显示所有数据
      ajax: {
        query: async (params: any, formValues: any) => {
          console.log('摄像头配置查询参数:', { params, formValues });

          // 构建分离的查询参数结构
          const requestParams = {
            query: {
              camera_name: formValues?.camera_name,
              brand: formValues?.brand,
              serial_number: formValues?.serial_number,
            },
            pagination: {
              page: params?.page?.currentPage || 1,
              pageSize: params?.page?.pageSize || 10,
            },
          };

          console.log('发送到后端的参数结构:', requestParams);

          const result = await getCameras(requestParams);
          console.log(
            '摄像头配置数据查询完成，数据条数:',
            result.items?.length || 0,
          );
          console.log('总数据条数:', result.total || 0);

          return {
            items: result.items,
            total: result.total, // 返回总条数用于分页
          };
        },
      },
    },
  },
}) as VxeTableGridOptions<CameraApi.CameraConfig>;

// API数据获取函数
async function getCameras(params?: {
  pagination?: {
    page?: number;
    pageSize?: number;
  };
  query?: {
    brand?: string;
    camera_name?: string;
    serial_number?: string;
  };
}): Promise<VideoListResponse> {
  try {
    const response = await getCameraList(params);
    return {
      items: response.items,
      total: response.total,
    };
  } catch (error) {
    console.error('获取摄像头配置数据失败:', error);
    // 如果API调用失败，返回空数据
    return {
      items: [],
      total: 0,
    };
  }
}

// 创建基础表格配置
function createGridOptions(): VxeTableGridOptions<VideoItemData> {
  return {
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    // 表格自适应配置
    autoResize: true, // 自动调整大小
    columnConfig: {
      resizable: true, // 列可调整大小
      useKey: true, // 使用key优化性能
    },
    // 表格布局配置
    border: true, // 显示边框
    stripe: true, // 斑马纹
    round: true, // 圆角
    pagerConfig: {
      enabled: true, // 启用分页
      pageSize: 10, // 每页显示条数
      pageSizes: [10, 20, 50, 100], // 可选的每页条数
      layouts: [
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total',
      ],
    },
    rowConfig: {
      keyField: 'serial_number',
    },
    toolbarConfig: {
      search: false,
      custom: true,
      export: true,
      refresh: { code: 'query' },
      zoom: true,
    },
    exportConfig: {},
  };
}
function onActionClick(e: OnActionClickParams<CameraApi.CameraConfig>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

/**
 * 将Element Plus的ElMessageBox.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
function confirm(message: string, title: string) {
  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  });
}

/**
 * 状态开关即将改变
 * @param newStatus 期望改变的状态值
 * @param row 行数据
 * @returns 返回false则中止改变，返回其他值（undefined、true）则允许改变
 */
async function onStatusChange(newStatus: number, row: CameraApi.CameraConfig) {
  const status: Recordable<string> = {
    0: '禁用',
    1: '启用',
  };
  try {
    await confirm(
      `你要将${row.name}的状态切换为 【${status[newStatus.toString()]}】 吗？`,
      `切换状态`,
    );
    await updateCamera(row.id, { status: newStatus });
    return true;
  } catch {
    return false;
  }
}

function onEdit(row: CameraApi.CameraConfig) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: CameraApi.CameraConfig) {
  const message = ElMessage({
    message: $t('ui.actionMessage.deleting', [row.camera_name]),
    duration: 0,
    type: 'info',
    showClose: false,
    icon: 'el-icon-loading',
    center: true,
    customClass: 'vben-message-loading',
    grouping: true,
    dangerouslyUseHTMLString: true,
    key: 'action_process_msg',
  });

  deleteCamera(row.id)
    .then(() => {
      message.close();
      ElMessage.success({
        message: $t('ui.actionMessage.deleteSuccess', [row.camera_name]),
        duration: 2000,
        type: 'success',
        showClose: true,
      });
      onRefresh();
    })
    .catch(() => {
      message.close();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid>
      <template #toolbar-tools>
        <ElButton type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', [$t('camera.cameraConfig.name')]) }}
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
<style lang="scss" scoped>
// 页面整体布局样式
.page-container {
  @apply h-full;
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.vxe-table) {
    font-size: 12px;
  }
}
</style>
