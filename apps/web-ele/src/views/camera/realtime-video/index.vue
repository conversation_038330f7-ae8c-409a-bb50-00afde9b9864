<script lang="ts" setup>
import type { TreeNodeData } from '#/api/camera';

import { computed, nextTick, onMounted, reactive, ref } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ElButton, ElInput, ElMessage, ElTooltip, ElTree } from 'element-plus';

import { getTreeNodes } from '#/api/camera';

import MonitorScreen from './components/MonitorScreen.vue';

// 监控摄像头树形数据
const treeData = ref<TreeNodeData[]>([]);
const loading = ref(false);

// ElTree 组件引用
const treeRef = ref<InstanceType<typeof ElTree>>();

// 当前选中的摄像头
const selectedCameras = ref<string[]>([]);

// 当前显示的四个监控画面（从树形数据中的摄像头节点获取）
const currentMonitorScreens = ref<
  Array<{
    id: string;
    status: 'connecting' | 'offline' | 'online';
    title: string;
  }>
>([]);

// 递归提取所有摄像头节点
const extractCameraNodes = (nodes: TreeNodeData[]): TreeNodeData[] => {
  const cameras: TreeNodeData[] = [];

  const traverse = (nodeList: TreeNodeData[]) => {
    for (const node of nodeList) {
      if (node.type === 'camera') {
        cameras.push(node);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    }
  };

  traverse(nodes);
  return cameras;
};

// 模拟摄像头状态（实际项目中应该从API获取）
const getRandomStatus = (): 'connecting' | 'offline' | 'online' => {
  const statuses: Array<'connecting' | 'offline' | 'online'> = [
    'online',
    'offline',
    'connecting',
  ];
  return statuses[Math.floor(Math.random() * statuses.length)];
};

// 递归查找节点的所有父节点ID
const getParentNodeIds = (nodes: TreeNodeData[], targetId: string, currentPath: string[] = []): string[] => {
  for (const node of nodes) {
    const newPath = [...currentPath, node.id];

    // 如果找到目标节点，返回路径（不包含目标节点本身）
    if (node.id === targetId) {
      return currentPath;
    }

    // 如果有子节点，递归查找
    if (node.children && node.children.length > 0) {
      const result = getParentNodeIds(node.children, targetId, newPath);
      if (result.length > 0) {
        // 如果在子树中找到了目标节点，返回完整路径
        return result;
      }
      // 检查直接子节点
      if (node.children.some(child => child.id === targetId)) {
        return newPath;
      }
    }
  }
  return [];
};

// 自动展开包含默认摄像头的路径
const autoExpandPathsForDefaultCameras = async (cameras: TreeNodeData[]) => {
  const pathsToExpand = new Set<string>();

  cameras.forEach(camera => {
    const parentIds = getParentNodeIds(treeData.value, camera.id);
    parentIds.forEach(id => pathsToExpand.add(id));
  });

  // 更新展开的节点列表，但限制总数
  const newExpandedKeys = Array.from(pathsToExpand);

  if (newExpandedKeys.length <= maxExpandedNodes) {
    // 使用 default-expanded-keys 进行初始展开（推荐方式）
    defaultExpandedKeys.value = newExpandedKeys;
    // 同时更新 expanded-keys 以保持一致性
    expandedKeys.value = newExpandedKeys;
    console.log('✅ 设置默认展开节点成功:', newExpandedKeys);
    console.log('🔍 defaultExpandedKeys:', defaultExpandedKeys.value);
    console.log('🔍 expandedKeys:', expandedKeys.value);
  } else {
    console.warn('自动展开的节点数量超过限制，跳过自动展开');
  }
};

// 更新监控画面数据
const updateMonitorScreens = () => {
  const cameraNodes = extractCameraNodes(treeData.value);

  // 取前4个摄像头作为监控画面，如果不足4个则用空数据填充
  currentMonitorScreens.value = Array.from({ length: 4 }, (_, index) => {
    const camera = cameraNodes[index];
    return camera
      ? {
          id: camera.id,
          title: camera.label,
          status: getRandomStatus(),
        }
      : {
          id: `empty-${index}`,
          title: `监控画面 ${index + 1}`,
          status: 'offline' as const,
        };
  });

  console.log('更新监控画面:', currentMonitorScreens.value);
};

// 加载树形数据
const loadTreeData = async () => {
  try {
    loading.value = true;
    const response = await getTreeNodes();
    treeData.value = response.items || [];
    console.log('树形数据加载成功:', treeData.value);

    // 加载完成后更新监控画面
    updateMonitorScreens();

    // 使用 nextTick 确保 DOM 更新后再展开节点
    await nextTick();

    // 强制展开默认监控画面的路径
    const cameraNodes = extractCameraNodes(treeData.value);
    const defaultCameras = cameraNodes.slice(0, 4);
    if (defaultCameras.length > 0) {
      // 延迟执行，确保 ElTree 完全渲染
      setTimeout(async () => {
        await autoExpandPathsForDefaultCameras(defaultCameras);
      }, 200);
    }
  } catch (error) {
    console.error('加载树形数据失败:', error);
    ElMessage.error('加载监控设备列表失败');
  } finally {
    loading.value = false;
  }
};

// 树节点点击事件
const handleNodeClick = (data: TreeNodeData) => {
  if (data.type === 'camera') {
    console.log('选中摄像头:', data.label);
    ElMessage.success(`选中摄像头: ${data.label}`);

    // 将选中的摄像头切换到第一个监控画面
    switchCameraToScreen(data, 0);
  }
};

// 切换摄像头到指定监控画面（支持位置对调）
const switchCameraToScreen = (
  camera: TreeNodeData,
  targetScreenIndex: number,
) => {
  if (
    targetScreenIndex < 0 ||
    targetScreenIndex >= currentMonitorScreens.value.length
  ) {
    return;
  }

  // 查找选中的摄像头是否已经在监控画面中
  const currentScreenIndex = currentMonitorScreens.value.findIndex(
    (screen) => screen.id === camera.id,
  );

  if (currentScreenIndex === -1) {
    // 摄像头不在监控画面中，直接切换到目标画面
    const targetScreen = currentMonitorScreens.value[targetScreenIndex];

    currentMonitorScreens.value[targetScreenIndex] = {
      id: camera.id,
      title: camera.label,
      status: getRandomStatus(),
    };

    if (targetScreen.id.startsWith('empty-')) {
      ElMessage.info(
        `${camera.label} 已切换到监控画面 ${targetScreenIndex + 1}`,
      );
    } else {
      ElMessage.info(
        `${camera.label} 已替换监控画面 ${targetScreenIndex + 1} 的 ${targetScreen.title}`,
      );
    }
  } else {
    // 摄像头已经在监控画面中，进行位置对调
    const targetScreen = currentMonitorScreens.value[targetScreenIndex];
    const currentScreen = currentMonitorScreens.value[currentScreenIndex];

    // 交换两个画面的内容
    currentMonitorScreens.value[targetScreenIndex] = {
      id: currentScreen.id,
      title: currentScreen.title,
      status: getRandomStatus(),
    };

    currentMonitorScreens.value[currentScreenIndex] = {
      id: targetScreen.id,
      title: targetScreen.title,
      status: getRandomStatus(),
    };

    if (targetScreen.id.startsWith('empty-')) {
      ElMessage.success(
        `${camera.label} 已移动到监控画面 ${targetScreenIndex + 1}`,
      );
    } else {
      ElMessage.success(`${camera.label} 与 ${targetScreen.title} 已交换位置`);
    }
  }
};

// 右键菜单相关
const contextMenuVisible = ref(false);
const contextMenuCamera = ref<null | TreeNodeData>(null);
const contextMenuPosition = ref({ x: 0, y: 0 });

// 计算可用于切换的监控画面（排除当前摄像头已占用的画面）
const availableScreensForSwitch = computed(() => {
  if (!contextMenuCamera.value) {
    return currentMonitorScreens.value.map((screen, index) => ({ ...screen, originalIndex: index }));
  }

  // 找到当前摄像头所在的画面索引
  const currentCameraScreenIndex = currentMonitorScreens.value.findIndex(
    screen => screen.id === contextMenuCamera.value!.id
  );

  // 返回除当前摄像头占用画面外的所有画面，并保留原始索引
  return currentMonitorScreens.value
    .map((screen, index) => ({ ...screen, originalIndex: index }))
    .filter((_, index) => index !== currentCameraScreenIndex);
});

// 处理树节点右键点击
const handleNodeRightClick = (event: MouseEvent, data: TreeNodeData) => {
  if (data.type === 'camera') {
    event.preventDefault();
    contextMenuCamera.value = data;
    contextMenuPosition.value = { x: event.clientX, y: event.clientY };
    contextMenuVisible.value = true;
  }
};

// 处理右键菜单选择
const handleContextMenuSelect = (filteredIndex: number) => {
  if (contextMenuCamera.value) {
    // 获取过滤后列表中对应项的原始索引
    const screenItem = availableScreensForSwitch.value[filteredIndex];
    const originalIndex = screenItem.originalIndex;

    switchCameraToScreen(contextMenuCamera.value, originalIndex);
    contextMenuVisible.value = false;
    contextMenuCamera.value = null;
  }
};

// 监控画面事件处理
const handleMonitorClick = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('点击监控画面:', title, screen);

  if (screen.id.startsWith('empty-')) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头`);
  } else {
    ElMessage.info(`当前监控画面: ${title}`);
  }
};

const handleMonitorFullscreen = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('全屏监控画面:', title, screen);

  if (screen.id.startsWith('empty-')) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法全屏`);
  } else {
    ElMessage.success(`${title} 进入全屏模式`);
  }
};

const handleMonitorRecord = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('录制监控画面:', title, screen);

  if (screen.id.startsWith('empty-')) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法录制`);
  } else {
    ElMessage.success(`开始录制 ${title}`);
  }
};

const handleMonitorScreenshot = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('截图监控画面:', title, screen);

  if (screen.id.startsWith('empty-')) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法截图`);
  } else {
    ElMessage.success(`${title} 截图成功`);
  }
};

// 大数据量优化：节点展开控制
const expandedKeys = ref<string[]>([]);
const defaultExpandedKeys = ref<string[]>([]); // 默认展开的节点（用于初始展开）
const maxExpandedNodes = 50; // 最大同时展开的节点数

// 节点展开事件处理
const handleNodeExpand = (data: TreeNodeData, node: any) => {
  // 限制同时展开的节点数量
  if (expandedKeys.value.length >= maxExpandedNodes) {
    ElMessage.warning(`为了性能考虑，最多同时展开 ${maxExpandedNodes} 个节点`);
    return;
  }

  if (!expandedKeys.value.includes(data.id)) {
    expandedKeys.value.push(data.id);
  }

  console.log(
    '节点展开:',
    data.label,
    '当前展开数量:',
    expandedKeys.value.length,
  );
};

// 节点折叠事件处理
const handleNodeCollapse = (data: TreeNodeData, node: any) => {
  const index = expandedKeys.value.indexOf(data.id);
  if (index !== -1) {
    expandedKeys.value.splice(index, 1);
  }

  console.log(
    '节点折叠:',
    data.label,
    '当前展开数量:',
    expandedKeys.value.length,
  );
};

// 大数据量优化：搜索功能
const searchKeyword = ref('');
const filteredTreeData = computed(() => {
  if (!searchKeyword.value.trim()) {
    return treeData.value;
  }

  return filterTreeByKeyword(treeData.value, searchKeyword.value.trim());
});

// 递归过滤树形数据
const filterTreeByKeyword = (
  nodes: TreeNodeData[],
  keyword: string,
): TreeNodeData[] => {
  const result: TreeNodeData[] = [];

  for (const node of nodes) {
    const nodeMatches = node.label
      .toLowerCase()
      .includes(keyword.toLowerCase());
    let filteredChildren: TreeNodeData[] = [];

    if (node.children && node.children.length > 0) {
      filteredChildren = filterTreeByKeyword(node.children, keyword);
    }

    // 如果节点本身匹配或有匹配的子节点，则包含此节点
    if (nodeMatches || filteredChildren.length > 0) {
      result.push({
        ...node,
        children:
          filteredChildren.length > 0 ? filteredChildren : node.children,
      });
    }
  }

  return result;
};

// 组件挂载时加载数据
onMounted(() => {
  loadTreeData();
});

const props = reactive({
  leftCollapsedWidth: 5,
  leftCollapsible: true, // 左侧可折叠
  leftMaxWidth: 50, // 左侧最大宽度百分比
  leftMinWidth: 20, // 左侧最小宽度百分比
  leftWidth: 30, // 左侧初始宽度百分比
  resizable: true, // 可拖动调整宽度
  rightWidth: 70,
  splitHandle: true, // 显示拖动手柄
  splitLine: true, // 显示拖动分隔线
});
</script>
<template>
  <ColPage auto-content-height v-bind="props">
    <template #left="{ isCollapsed, expand, collapse }">
      <div
        class="bg-card border-border mr-2 h-full rounded-[var(--radius)] border"
      >
        <div
          v-if="isCollapsed"
          @click="expand"
          class="flex h-full items-center justify-center"
        >
          <ElTooltip content="点击展开左侧">
            <ElButton size="small" text @click="expand">
              <IconifyIcon
                icon="mdi:chevron-right"
                class="text-xl md:text-[32px]"
              />
            </ElButton>
          </ElTooltip>
        </div>
        <!--  左侧树形控件  -->
        <div
          v-else
          :style="{ minWidth: '200px' }"
          class="flex h-full flex-col p-3 text-sm"
        >
          <!-- 树形控件标题 -->
          <div class="mb-3">
            <div
              class="border-border mb-2 flex items-center justify-between border-b pb-2"
            >
              <h3 class="text-foreground text-xl font-medium">监控设备</h3>
              <div class="flex items-center gap-1">
                <ElButton
                  size="small"
                  text
                  @click="loadTreeData"
                  :loading="loading"
                >
                  <IconifyIcon
                    icon="mdi:refresh"
                    class="text-xl md:text-[32px]"
                  />
                </ElButton>
                <ElButton size="small" text @click="collapse">
                  <IconifyIcon
                    icon="mdi:chevron-left"
                    class="text-xl md:text-[32px]"
                  />
                </ElButton>
              </div>
            </div>
            <!-- 搜索框 -->
            <ElInput
              v-model="searchKeyword"
              placeholder="搜索设备..."
              size="default"
              clearable
              class="mb-2"
            >
              <template #prefix>
                <IconifyIcon icon="mdi:magnify" class="text-muted-foreground" />
              </template>
            </ElInput>
          </div>

          <!-- 树形控件 -->
          <div class="flex-1 overflow-auto" v-loading="loading">
            <ElTree
              ref="treeRef"
              :data="filteredTreeData"
              :props="{ children: 'children', label: 'label' }"
              node-key="id"
              :default-expand-all="false"
              :highlight-current="true"
              :lazy="false"
              :render-after-expand="false"
              :check-strictly="true"
              :expand-on-click-node="false"
              :auto-expand-parent="true"
              :default-expanded-keys="defaultExpandedKeys"
              :expanded-keys="expandedKeys"
              @node-click="handleNodeClick"
              @node-expand="handleNodeExpand"
              @node-collapse="handleNodeCollapse"
              class="tree-container"
            >
              <template #default="{ node, data }">
                <div
                  class="flex items-center gap-3 py-1.5"
                  @contextmenu="(event) => handleNodeRightClick(event, data)"
                >
                  <IconifyIcon
                    :icon="data.icon || 'mdi:folder'"
                    class="flex-shrink-0 text-lg"
                    :class="[
                      data.type === 'camera'
                        ? 'text-primary'
                        : 'text-foreground/70',
                    ]"
                  />
                  <span
                    class="text-base leading-relaxed"
                    :class="[
                      data.type === 'camera'
                        ? 'text-foreground'
                        : 'text-muted-foreground font-medium',
                    ]"
                  >
                    {{ data.label }}
                  </span>
                </div>
              </template>
            </ElTree>
          </div>
        </div>
      </div>
    </template>

    <!-- 右侧监控画面区域 -->
    <div class="ml-2 flex h-full flex-col text-sm">
      <!-- 顶部工具栏 -->
      <div
        class="bg-card border-border mb-2 rounded-[var(--radius)] border p-3"
      >
        <div class="flex items-center justify-between">
          <h2 class="text-foreground text-xl font-semibold">实时监控</h2>
          <div class="flex items-center gap-2">
            <ElButton size="default" type="primary">
              <IconifyIcon icon="mdi:fullscreen" class="mr-1 text-lg" />
              全屏
            </ElButton>
            <ElButton size="default">
              <IconifyIcon icon="mdi:record" class="mr-1 text-lg" />
              录制
            </ElButton>
            <ElButton size="default">
              <IconifyIcon icon="mdi:camera" class="mr-1 text-lg" />
              截图
            </ElButton>
          </div>
        </div>
      </div>

      <!-- 监控画面网格 -->
      <div
        class="bg-card border-border flex-1 rounded-[var(--radius)] border p-3"
      >
        <div class="grid h-full grid-cols-2 gap-3">
          <!-- 动态监控画面 -->
          <MonitorScreen
            v-for="(screen, index) in currentMonitorScreens"
            :key="screen.id"
            :title="screen.title"
            :status="screen.status"
            :placeholder="
              screen.id.startsWith('empty-') ? '暂无摄像头' : '暂无信号'
            "
            @click="(title) => handleMonitorClick(title, index)"
            @fullscreen="(title) => handleMonitorFullscreen(title, index)"
            @record="(title) => handleMonitorRecord(title, index)"
            @screenshot="(title) => handleMonitorScreenshot(title, index)"
          />
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <Teleport to="body">
      <div
        v-if="contextMenuVisible"
        class="fixed z-50 min-w-[160px] rounded-lg border-2 border-border bg-card py-1 shadow-xl ring-1 ring-black/5"
        :style="{
          left: `${contextMenuPosition.x}px`,
          top: `${contextMenuPosition.y}px`,
        }"
        @click.stop
        @contextmenu.prevent
      >
        <div class="border-b border-border px-3 py-2 text-sm text-muted-foreground">
          {{ contextMenuCamera?.label }}
        </div>
        <div
          v-for="(screen, index) in availableScreensForSwitch"
          :key="screen.originalIndex"
          class="flex cursor-pointer items-center justify-between px-3 py-2 hover:bg-accent hover:text-accent-foreground transition-colors"
          @click="handleContextMenuSelect(index)"
        >
          <span>切换到画面 {{ screen.originalIndex + 1 }}</span>
          <span class="text-sm text-muted-foreground">{{ screen.title }}</span>
        </div>
      </div>
    </Teleport>

    <!-- 点击其他地方关闭右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="fixed inset-0 z-40"
      @click="contextMenuVisible = false"
      @contextmenu.prevent="contextMenuVisible = false"
    ></div>
  </ColPage>
</template>

<style scoped>
/* 全局字体大小设置 - 基准14px */
.text-sm {
  font-size: 14px;
}

/* 树形控件样式优化 */
.tree-container :deep(.el-tree-node__content) {
  height: 36px;
  padding: 0 10px;
  border-radius: 6px;
  margin: 2px 0;
  transition: all 0.2s ease;
}

.tree-container :deep(.el-tree-node__content:hover) {
  background-color: var(--el-fill-color-light);
}

.tree-container :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.tree-container :deep(.el-tree-node__expand-icon) {
  color: var(--el-text-color-regular);
  font-size: 16px;
  width: 20px;
  height: 20px;
}

.tree-container :deep(.el-tree-node__expand-icon.is-leaf) {
  color: transparent;
}

/* 监控画面容器样式 */
.aspect-video {
  aspect-ratio: 16 / 9;
}

/* 状态指示器动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
