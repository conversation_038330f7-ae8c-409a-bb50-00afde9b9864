<script lang="ts" setup>
import { computed } from 'vue';

import { IconifyIcon } from '@vben/icons';

// 定义组件属性
interface Props {
  title: string; // 监控画面标题
  status: 'online' | 'offline' | 'connecting'; // 连接状态
  placeholder?: string; // 占位符文字
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '暂无信号',
});

// 状态配置
const statusConfig = computed(() => {
  const configs = {
    online: {
      color: 'bg-green-500',
      text: '在线',
      textColor: 'text-green-400',
    },
    offline: {
      color: 'bg-red-500 animate-pulse',
      text: '离线',
      textColor: 'text-red-400',
    },
    connecting: {
      color: 'bg-yellow-500',
      text: '连接中',
      textColor: 'text-yellow-400',
    },
  };
  return configs[props.status];
});

// 定义事件
const emit = defineEmits<{
  click: [title: string];
  fullscreen: [title: string];
  record: [title: string];
  screenshot: [title: string];
}>();

// 事件处理
const handleClick = () => {
  emit('click', props.title);
};

const handleFullscreen = () => {
  emit('fullscreen', props.title);
};

const handleRecord = () => {
  emit('record', props.title);
};

const handleScreenshot = () => {
  emit('screenshot', props.title);
};
</script>

<template>
  <div
    class="relative bg-black rounded-lg overflow-hidden aspect-video cursor-pointer group"
    @click="handleClick"
  >
    <!-- 监控画面内容区域 -->
    <div class="absolute inset-0 flex items-center justify-center">
      <div class="text-center text-white/70">
        <IconifyIcon icon="mdi:cctv" class="text-6xl mb-2" />
        <p class="text-base">{{ title }}</p>
        <p class="text-sm text-white/50">{{ placeholder }}</p>
      </div>
    </div>

    <!-- 画面标题栏 -->
    <div class="absolute top-0 left-0 right-0 bg-black/50 text-white px-3 py-1">
      <div class="flex items-center justify-between">
        <span class="truncate flex-1 mr-2 text-lg md:text-xl">{{ title }}</span>
        <div class="flex items-center gap-1 flex-shrink-0">
          <div :class="['w-2 h-2 rounded-full', statusConfig.color]"></div>
          <span :class="['text-sm', statusConfig.textColor]">{{ statusConfig.text }}</span>
        </div>
      </div>
    </div>

    <!-- 悬停时显示的操作按钮 -->
    <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
      <div class="flex items-center gap-1">
        <button
          @click.stop="handleFullscreen"
          class="bg-black/60 hover:bg-black/80 text-white p-1.5 rounded text-sm transition-colors"
          title="全屏"
        >
          <IconifyIcon icon="mdi:fullscreen" class="text-lg md:text-[32px]" />
        </button>
        <button
          @click.stop="handleRecord"
          class="bg-black/60 hover:bg-black/80 text-white p-1.5 rounded text-sm transition-colors"
          title="录制"
        >
          <IconifyIcon icon="mdi:record"  class="text-lg md:text-[32px]" />
        </button>
        <button
          @click.stop="handleScreenshot"
          class="bg-black/60 hover:bg-black/80 text-white p-1.5 rounded text-sm transition-colors"
          title="截图"
        >
          <IconifyIcon icon="mdi:camera"  class="text-lg md:text-[32px]" />
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 监控画面容器样式 */
.aspect-video {
  aspect-ratio: 16 / 9;
}

/* 状态指示器动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
