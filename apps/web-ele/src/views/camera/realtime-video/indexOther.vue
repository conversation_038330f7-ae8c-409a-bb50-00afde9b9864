<script lang="ts" setup>

import { onMounted, reactive, ref } from 'vue';

import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import { ElButton, ElMessage, ElTooltip, ElTree, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus';

import { getTreeNodes, type TreeNodeData } from '#/api/camera';

import MonitorScreen from './components/MonitorScreen.vue';

// 监控摄像头树形数据
const treeData = ref<TreeNodeData[]>([]);
const loading = ref(false);

// 当前选中的摄像头
const selectedCameras = ref<string[]>([]);

// 固定的四个监控画面配置，每个画面对应一个特定的摄像头节点ID
const monitorScreenConfig = [
  { cameraId: '1-1-1-1-1', defaultTitle: '监控画面 1' }, // N-D2-5-1(纸箱打包机)
  { cameraId: '1-1-1-2-1', defaultTitle: '监控画面 2' }, // N-D2-5-2(包装生产线)
  { cameraId: '1-1-1-2-2', defaultTitle: '监控画面 3' }, // N-D2-5-3(质检设备)
  { cameraId: '1-1-2-1-1', defaultTitle: '监控画面 4' }, // N-D3-1-1(生产线A)
];

// 当前显示的四个监控画面数据
const currentMonitorScreens = ref<Array<{
  id: string;
  title: string;
  status: 'online' | 'offline' | 'connecting';
  cameraNode?: TreeNodeData | null;
}>>([]);

// 递归查找指定ID的摄像头节点
const findCameraNodeById = (nodes: TreeNodeData[], targetId: string): TreeNodeData | null => {
  for (const node of nodes) {
    if (node.id === targetId && node.type === 'camera') {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findCameraNodeById(node.children, targetId);
      if (found) return found;
    }
  }
  return null;
};

// 获取摄像头节点的完整路径
const getCameraNodePath = (nodes: TreeNodeData[], targetId: string, path: string[] = []): string[] => {
  for (const node of nodes) {
    const currentPath = [...path, node.label];

    if (node.id === targetId && node.type === 'camera') {
      return currentPath;
    }

    if (node.children && node.children.length > 0) {
      const found = getCameraNodePath(node.children, targetId, currentPath);
      if (found.length > 0) return found;
    }
  }
  return [];
};

// 模拟摄像头状态（实际项目中应该从API获取）
const getRandomStatus = (): 'online' | 'offline' | 'connecting' => {
  const statuses: Array<'online' | 'offline' | 'connecting'> = ['online', 'offline', 'connecting'];
  return statuses[Math.floor(Math.random() * statuses.length)];
};

// 更新监控画面数据
const updateMonitorScreens = () => {
  currentMonitorScreens.value = monitorScreenConfig.map((config, index) => {
    // 根据配置的摄像头ID查找对应的节点
    const cameraNode = findCameraNodeById(treeData.value, config.cameraId);

    if (cameraNode) {
      return {
        id: cameraNode.id,
        title: cameraNode.label,
        status: getRandomStatus(),
        cameraNode: cameraNode,
      };
    } else {
      return {
        id: `empty-${index}`,
        title: config.defaultTitle,
        status: 'offline' as const,
        cameraNode: null,
      };
    }
  });

  console.log('更新监控画面:', currentMonitorScreens.value);
};

// 加载树形数据
const loadTreeData = async () => {
  try {
    loading.value = true;
    const response = await getTreeNodes();
    treeData.value = response.items || [];
    console.log('树形数据加载成功:', treeData.value);

    // 加载完成后更新监控画面
    updateMonitorScreens();
  } catch (error) {
    console.error('加载树形数据失败:', error);
    ElMessage.error('加载监控设备列表失败');
  } finally {
    loading.value = false;
  }
};

// 树节点点击事件
const handleNodeClick = (data: TreeNodeData) => {
  if (data.type === 'camera') {
    console.log('选中摄像头:', data.label);

    // 查找该摄像头对应的监控画面
    const screenIndex = currentMonitorScreens.value.findIndex(screen => screen.id === data.id);
    if (screenIndex !== -1) {
      ElMessage.success(`摄像头 ${data.label} 对应监控画面 ${screenIndex + 1}`);
    } else {
      ElMessage.info(`摄像头 ${data.label} 暂未分配到监控画面`);
    }
  }
};

// 切换摄像头到指定监控画面
const switchCameraToScreen = (camera: TreeNodeData, screenIndex: number) => {
  if (screenIndex >= 0 && screenIndex < currentMonitorScreens.value.length) {
    // 更新配置
    monitorScreenConfig[screenIndex].cameraId = camera.id;

    // 更新当前显示的监控画面数据
    currentMonitorScreens.value[screenIndex] = {
      id: camera.id,
      title: camera.label,
      status: getRandomStatus(),
      cameraNode: camera,
    };

    ElMessage.info(`${camera.label} 已切换到监控画面 ${screenIndex + 1}`);
  }
};

// 右键菜单相关
const contextMenuVisible = ref(false);
const contextMenuCamera = ref<TreeNodeData | null>(null);
const contextMenuPosition = ref({ x: 0, y: 0 });

// 处理树节点右键点击
const handleNodeRightClick = (event: MouseEvent, data: TreeNodeData) => {
  if (data.type === 'camera') {
    event.preventDefault();
    contextMenuCamera.value = data;
    contextMenuPosition.value = { x: event.clientX, y: event.clientY };
    contextMenuVisible.value = true;
  }
};

// 处理右键菜单选择
const handleContextMenuSelect = (screenIndex: number) => {
  if (contextMenuCamera.value) {
    switchCameraToScreen(contextMenuCamera.value, screenIndex);
    contextMenuVisible.value = false;
    contextMenuCamera.value = null;
  }
};

// 监控画面事件处理
const handleMonitorClick = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('点击监控画面:', title, screen);

  if (!screen.cameraNode) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头`);
  } else {
    ElMessage.info(`当前监控画面: ${title} (${screen.cameraNode.label})`);
  }
};

const handleMonitorFullscreen = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('全屏监控画面:', title, screen);

  if (!screen.cameraNode) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法全屏`);
  } else {
    ElMessage.success(`${screen.cameraNode.label} 进入全屏模式`);
  }
};

const handleMonitorRecord = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('录制监控画面:', title, screen);

  if (!screen.cameraNode) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法录制`);
  } else {
    ElMessage.success(`开始录制 ${screen.cameraNode.label}`);
  }
};

const handleMonitorScreenshot = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('截图监控画面:', title, screen);

  if (!screen.cameraNode) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法截图`);
  } else {
    ElMessage.success(`${screen.cameraNode.label} 截图成功`);
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadTreeData();
});

const props = reactive({
  leftCollapsedWidth: 5,
  leftCollapsible: true, // 左侧可折叠
  leftMaxWidth: 50, // 左侧最大宽度百分比
  leftMinWidth: 20, // 左侧最小宽度百分比
  leftWidth: 30, // 左侧初始宽度百分比
  resizable: true, // 可拖动调整宽度
  rightWidth: 70,
  splitHandle: true, // 显示拖动手柄
  splitLine: true, // 显示拖动分隔线
});
</script>
<template>
  <ColPage auto-content-height v-bind="props">
    <template #left="{ isCollapsed, expand, collapse }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton size="small" text @click="expand">
            <IconifyIcon icon="mdi:chevron-right" class="text-xl" />
          </ElButton>
        </ElTooltip>
      </div>
      <!--  左侧树形控件  -->
      <div
        v-else
        :style="{ minWidth: '200px' }"
        class="border-border bg-card mr-2 flex h-full flex-col rounded-[var(--radius)] border p-2"
      >
        <!-- 树形控件标题 -->
        <div
          class="border-border mb-3 flex items-center justify-between border-b pb-2"
        >
          <h3 class="text-foreground text-sm font-medium">监控设备</h3>
          <div class="flex items-center gap-1">
            <ElButton
              size="small"
              text
              @click="loadTreeData"
              :loading="loading"
            >
              <IconifyIcon icon="mdi:refresh" class="text-lg" />
            </ElButton>
            <ElButton size="small" text @click="collapse">
              <IconifyIcon icon="mdi:chevron-left" class="text-xl" />
            </ElButton>
          </div>
        </div>

        <!-- 树形控件 -->
        <div class="flex-1 overflow-auto" v-loading="loading">
          <ElTree
            :data="treeData"
            :props="{ children: 'children', label: 'label' }"
            node-key="id"
            :default-expand-all="true"
            :highlight-current="true"
            @node-click="handleNodeClick"
            class="tree-container"
          >
            <template #default="{ node, data }">
              <div
                class="flex items-center gap-2 py-1"
                @contextmenu="(event) => handleNodeRightClick(event, data)"
              >
                <IconifyIcon
                  :icon="data.icon || 'mdi:folder'"
                  class="text-base"
                  :class="[
                    data.type === 'camera' ? 'text-blue-500' : 'text-gray-500',
                  ]"
                />
                <span
                  class="text-sm"
                  :class="[
                    data.type === 'camera'
                      ? 'text-foreground'
                      : 'text-muted-foreground font-medium',
                  ]"
                >
                  {{ data.label }}
                </span>
                <span
                  v-if="data.type === 'camera'"
                  class="text-xs text-muted-foreground ml-auto"
                >
                  右键切换
                </span>
              </div>
            </template>
          </ElTree>
        </div>
      </div>
    </template>

    <!-- 右侧监控画面区域 -->
    <div class="ml-2 flex h-full flex-col">
      <!-- 顶部工具栏 -->
      <div
        class="bg-card border-border mb-2 rounded-[var(--radius)] border p-3"
      >
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-foreground text-lg font-semibold">实时监控</h2>
            <p class="text-muted-foreground text-xs mt-1">
              每个监控画面对应一个固定的摄像头节点，右键点击摄像头可切换画面
            </p>
          </div>
          <div class="flex items-center gap-2">
            <ElButton size="small" type="primary">
              <IconifyIcon icon="mdi:fullscreen" class="mr-1" />
              全屏
            </ElButton>
            <ElButton size="small">
              <IconifyIcon icon="mdi:record" class="mr-1" />
              录制
            </ElButton>
            <ElButton size="small">
              <IconifyIcon icon="mdi:camera" class="mr-1" />
              截图
            </ElButton>
          </div>
        </div>
      </div>

      <!-- 监控画面网格 -->
      <div
        class="bg-card border-border flex-1 rounded-[var(--radius)] border p-3"
      >
        <div class="grid h-full grid-cols-2 gap-3">
          <!-- 动态监控画面 -->
          <MonitorScreen
            v-for="(screen, index) in currentMonitorScreens"
            :key="screen.id"
            :title="screen.title"
            :status="screen.status"
            :placeholder="!screen.cameraNode ? '暂无摄像头' : '暂无信号'"
            @click="(title) => handleMonitorClick(title, index)"
            @fullscreen="(title) => handleMonitorFullscreen(title, index)"
            @record="(title) => handleMonitorRecord(title, index)"
            @screenshot="(title) => handleMonitorScreenshot(title, index)"
          />
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <Teleport to="body">
      <div
        v-if="contextMenuVisible"
        class="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[120px]"
        :style="{
          left: contextMenuPosition.x + 'px',
          top: contextMenuPosition.y + 'px'
        }"
        @click.stop
        @contextmenu.prevent
      >
        <div class="px-3 py-2 text-xs text-gray-500 border-b border-gray-100">
          {{ contextMenuCamera?.label }}
        </div>
        <div
          v-for="(screen, index) in currentMonitorScreens"
          :key="index"
          class="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer flex items-center justify-between"
          @click="handleContextMenuSelect(index)"
        >
          <span>切换到画面 {{ index + 1 }}</span>
          <span class="text-xs text-gray-400">{{ screen.title }}</span>
        </div>
      </div>
    </Teleport>

    <!-- 点击其他地方关闭右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="fixed inset-0 z-40"
      @click="contextMenuVisible = false"
      @contextmenu.prevent="contextMenuVisible = false"
    ></div>
  </ColPage>
</template>

<style scoped>
/* 树形控件样式优化 */
.tree-container :deep(.el-tree-node__content) {
  height: 32px;
  padding: 0 8px;
  border-radius: 4px;
  margin: 1px 0;
  transition: all 0.2s ease;
}

.tree-container :deep(.el-tree-node__content:hover) {
  background-color: var(--el-fill-color-light);
}

.tree-container :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.tree-container :deep(.el-tree-node__expand-icon) {
  color: var(--el-text-color-regular);
}

.tree-container :deep(.el-tree-node__expand-icon.is-leaf) {
  color: transparent;
}

/* 监控画面容器样式 */
.aspect-video {
  aspect-ratio: 16 / 9;
}

/* 状态指示器动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
