{"title": "System Management", "dept": {"name": "Department", "title": "Department Management", "deptName": "Department Name", "status": "Status", "createTime": "Create Time", "remark": "Remark", "operation": "Operation", "parentDept": "Parent Department"}, "menu": {"title": "Menu Management", "parent": "<PERSON><PERSON>", "menuTitle": "Title", "menuName": "<PERSON>u Name", "name": "<PERSON><PERSON>", "type": "Type", "typeCatalog": "Catalog", "typeMenu": "<PERSON><PERSON>", "typeButton": "<PERSON><PERSON>", "typeLink": "Link", "typeEmbedded": "Embedded", "icon": "Icon", "activeIcon": "Active Icon", "activePath": "Active Path", "path": "Route Path", "component": "Component", "status": "Status", "authCode": "Auth Code", "badge": "Badge", "operation": "Operation", "linkSrc": "Link Address", "affixTab": "Affix In Tabs", "keepAlive": "Keep Alive", "hideInMenu": "Hide In Menu", "hideInTab": "Hide In Tabbar", "hideChildrenInMenu": "Hide Children In Menu", "hideInBreadcrumb": "Hide In Breadcrumb", "advancedSettings": "Other Settings", "activePathMustExist": "The path could not find a valid menu", "activePathHelp": "When jumping to the current route, \nthe menu path that needs to be activated must be specified when it does not display in the navigation menu.", "badgeType": {"title": "Badge Type", "dot": "Dot", "normal": "Text", "none": "None"}, "badgeVariants": "Badge Style"}, "role": {"title": "Role Management", "list": "Role List", "name": "Role", "roleName": "Role Name", "id": "Role ID", "status": "Status", "remark": "Remark", "createTime": "Creation Time", "operation": "Operation", "permissions": "Permissions", "setPermissions": "Permissions"}, "energyReport": {"title": "Enterprise Energy Report", "description": "Display detailed data of enterprise load and photovoltaic power generation", "time": "Time", "date": "Date", "month": "Month", "load": "Load(kW·h)", "wind": "Wind Power(kW·h)", "storage": "Energy Storage(kW·h)", "chargingPile": "Charging Pile(kW·h)", "grid": "Grid(kW·h)", "solar": "Solar(kW·h)", "consumption": "Consumption", "gridConnection": "Grid Connection", "total": "Total", "charge": "Charge", "discharge": "Discharge", "timeSlots": {"sharp": "<PERSON>", "peak": "Peak", "flat": "Flat", "valley": "Valley", "deepValley": "Deep Valley", "total": "Total"}, "refreshData": "Refresh Data", "tabs": {"monthly": "Monthly Report", "yearly": "Yearly Report"}, "form": {"enterpriseSelect": "Enterprise Selection", "enterprisePlaceholder": "Please select enterprise", "enterpriseSearchPlaceholder": "Enter enterprise name to search", "dateRange": "Date Range", "monthRange": "Month Range", "startDate": "Start Date", "endDate": "End Date", "startMonth": "Start Month", "endMonth": "End Month", "separator": "to"}, "footer": {"total": "Total"}}, "incomeReport": {"title": "Enterprise Income Report", "description": "Display detailed data of enterprise income from various sources", "time": "Time", "date": "Date", "month": "Month", "chargingPile": "Charging <PERSON>", "electricityAmount": "Electricity Amount(kW·h)", "chargingPileIncome": "Income(¥)", "windPower": "Wind Power", "windConsumptionAmount": "Consumption Amount(kW·h)", "windGridConnectionAmount": "Grid Connection Amount(kW·h)", "windIncomeAmount": "Income(¥)", "totalIncome": "Total", "totalIncomeAmount": "Income(¥)", "storageIncome": "Energy Storage", "chargeAmount": "Charge Amount(kW·h)", "dischargeAmount": "Discharge Amount(kW·h)", "storageIncomeAmount": "Income(¥)", "solarIncome": "Solar", "consumption": "Consumption", "consumptionAmount": "Consumption Amount(kW·h)", "gridConnection": "Grid Connection", "gridConnectionAmount": "Grid Connection Amount(kW·h)", "income": "Income(¥)", "total": "Total", "charge": "Charge", "discharge": "Discharge", "timeSlots": {"sharp": "<PERSON>", "peak": "Peak", "flat": "Flat", "valley": "Valley", "deepValley": "Deep Valley", "total": "Total"}, "refreshData": "Refresh Data", "tabs": {"monthly": "Monthly Report", "yearly": "Yearly Report"}, "form": {"enterpriseSelect": "Enterprise Selection", "enterprisePlaceholder": "Please select enterprise", "enterpriseSearchPlaceholder": "Enter enterprise name to search", "dateRange": "Date Range", "monthRange": "Month Range", "startDate": "Start Date", "endDate": "End Date", "startMonth": "Start Month", "endMonth": "End Month", "separator": "to"}, "footer": {"total": "Total"}}, "photovoltaicIncomeReport": {"title": "Photovoltaic Income Report", "description": "Display detailed data of photovoltaic power generation system income", "time": "Time", "date": "Date", "month": "Month", "consumption": "Consumption", "sharpAmount": "Sharp Amount(kWh)", "sharpIncome": "Sharp Income(¥)", "peakAmount": "Peak Amount(kWh)", "peakIncome": "Peak Income(¥)", "flatAmount": "Flat Amount(kWh)", "flatIncome": "Flat Income(¥)", "valleyAmount": "Valley Amount(kWh)", "valleyIncome": "Valley Income(¥)", "deepValleyAmount": "Deep Valley Amount(kWh)", "deepValleyIncome": "Deep Valley Income(¥)", "consumptionTotalIncome": "Total Income", "grid": "Grid", "gridAmount": "Amount(kWh)", "gridIncome": "Income(¥)", "total": "Total", "totalAmount": "Amount(kWh)", "totalIncome": "Income(¥)", "tabs": {"monthly": "Monthly Report", "yearly": "Yearly Report"}, "form": {"enterpriseSelect": "Enterprise Selection", "enterprisePlaceholder": "Please select enterprise", "enterpriseSearchPlaceholder": "Enter enterprise name to search", "dateRange": "Date Range", "monthRange": "Month Range", "startDate": "Start Date", "endDate": "End Date", "startMonth": "Start Month", "endMonth": "End Month", "separator": "to"}, "footer": {"total": "Total"}}, "energyStorageIncomeReport": {"title": "Energy Storage Income Report", "description": "Display detailed data of energy storage system income", "time": "Time", "date": "Date", "month": "Month", "chargingElectricity": "Charging Electricity(kW·h)", "dischargingElectricity": "Discharging Electricity(kW·h)", "chargingCost": "Charging Cost(¥)", "dischargingIncome": "Discharging Income(¥)", "totalNetIncome": "Total Net Income(¥)", "sharp": "<PERSON>", "peak": "Peak", "flat": "Flat", "valley": "Valley", "deepValley": "Deep Valley", "chargingPile": "Charging <PERSON>", "electricityAmount": "Electricity Amount(kW·h)", "chargingPileIncome": "Income(¥)", "windPower": "Wind Power", "windConsumptionAmount": "Consumption Amount(kW·h)", "windGridConnectionAmount": "Grid Connection Amount(kW·h)", "windIncomeAmount": "Income(¥)", "totalIncome": "Total", "totalIncomeAmount": "Income(¥)", "storageIncome": "Energy Storage", "chargeAmount": "Charge Amount(kW·h)", "dischargeAmount": "Discharge Amount(kW·h)", "storageIncomeAmount": "Income(¥)", "tabs": {"monthly": "Monthly Report", "yearly": "Yearly Report"}, "form": {"enterpriseSelect": "Enterprise Selection", "enterprisePlaceholder": "Please select enterprise", "enterpriseSearchPlaceholder": "Enter enterprise name to search", "dateRange": "Date Range", "monthRange": "Month Range", "startDate": "Start Date", "endDate": "End Date", "startMonth": "Start Month", "endMonth": "End Month", "separator": "to"}, "footer": {"total": "Total"}, "chart": {"totalChargingElectricity": "Total Charging Electricity", "totalDischargingElectricity": "Total Discharging Electricity", "totalChargingCost": "Total Charging Cost", "totalDischargingIncome": "Total Discharging Income", "unitElectricity": "kWh", "unitCurrency": "¥", "saveAsImage": "Save as Image", "dataZoom": "Data Zoom", "dataZoomReset": "Reset Zoom", "restore": "Rest<PERSON>", "dataView": "Data View"}}, "chargingIncomeReport": {"title": "Charging Income Report", "description": "Display detailed data of charging pile income", "time": "Time", "date": "Date", "month": "Month", "sharp": "<PERSON>", "peak": "Peak", "flat": "Flat", "valley": "Valley", "deepValley": "Deep Valley", "sharpElectricity": "Electricity(kW·h)", "sharpCost": "Cost(¥)", "peakElectricity": "Electricity(kW·h)", "peakCost": "Cost(¥)", "flatElectricity": "Electricity(kW·h)", "flatCost": "Cost(¥)", "valleyElectricity": "Electricity(kW·h)", "valleyCost": "Cost(¥)", "deepValleyElectricity": "Electricity(kW·h)", "deepValleyCost": "Cost(¥)", "chargingAmount": "Charging Amount(¥)", "netProfit": "Net Profit(¥)", "tabs": {"monthly": "Monthly Report", "yearly": "Yearly Report"}, "form": {"enterpriseSelect": "Enterprise Selection", "enterprisePlaceholder": "Please select enterprise", "enterpriseSearchPlaceholder": "Enter enterprise name to search", "dateRange": "Date Range", "monthRange": "Month Range", "startDate": "Start Date", "endDate": "End Date", "startMonth": "Start Month", "endMonth": "End Month", "separator": "to"}, "footer": {"total": "Total"}}, "windPowerIncomeReport": {"title": "Wind Power Income Report", "description": "Display detailed data of wind power generation income", "time": "Time", "date": "Date", "month": "Month", "sharp": "<PERSON>", "peak": "Peak", "flat": "Flat", "valley": "Valley", "deepValley": "Deep Valley", "sharpElectricity": "Electricity(kW·h)", "sharpIncome": "Income(¥)", "peakElectricity": "Electricity(kW·h)", "peakIncome": "Income(¥)", "flatElectricity": "Electricity(kW·h)", "flatIncome": "Income(¥)", "valleyElectricity": "Electricity(kW·h)", "valleyIncome": "Income(¥)", "deepValleyElectricity": "Electricity(kW·h)", "deepValleyIncome": "Income(¥)", "totalAmount": "Total Generation(kW·h)", "totalIncome": "Total Income(¥)", "tabs": {"monthly": "Monthly Report", "yearly": "Yearly Report"}, "form": {"enterpriseSelect": "Enterprise Selection", "enterprisePlaceholder": "Please select enterprise", "enterpriseSearchPlaceholder": "Enter enterprise name to search", "dateRange": "Date Range", "monthRange": "Month Range", "startDate": "Start Date", "endDate": "End Date", "startMonth": "Start Month", "endMonth": "End Month", "separator": "to"}, "footer": {"total": "Total"}}}