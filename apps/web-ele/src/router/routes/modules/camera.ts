import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    name: 'Camera',
    path: '/camera',
    meta: {
      icon: 'ic:baseline-view-in-ar',
      keepAlive: true,
      order: 1000,
      title: '视频监控',
      hideInMenu: false,
    },
    children: [
      {
        meta: {
          title: '视频配置',
        },
        name: 'CameraConfig',
        path: 'camera-config',
        component: () => import('#/views/camera/camera-config/index.vue'),
      },
      {
        meta: {
          title: '实时画面',
        },
        name: 'RealtimeVideo',
        path: 'realtime-video',
        component: () => import('#/views/camera/realtime-video/index.vue'),
      },
    ],
  },
];

export default routes;
