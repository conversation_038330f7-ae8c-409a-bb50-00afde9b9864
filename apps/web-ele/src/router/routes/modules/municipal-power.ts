import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:transmission-tower',
      order: 5,
      title: $t('page.other.municipalPower'),
    },
    name: 'MunicipalPower',
    path: '/municipal-power',
    children: [
      {
        name: 'TransformerMonitoring',
        path: '/municipal-power/transformer-monitoring',
        component: () =>
          import('#/views/municipal_power/transformer_monitoring/index.vue'),
        meta: {
          title: $t('page.other.transformerMonitoring'),
          icon: 'mdi:transformer',
        },
      },
      {
        name: 'PowerExtremes',
        path: '/municipal-power/power-extremes',
        component: () =>
          import('#/views/municipal_power/power_extremes/index.vue'),
        meta: {
          title: $t('page.other.powerExtremes'),
          icon: 'mdi:chart-line-variant',
        },
      },
      {
        name: 'PowerCurve',
        path: '/municipal-power/power-curve',
        component: () =>
          import('#/views/municipal_power/power_curve/index.vue'),
        meta: {
          title: $t('page.other.powerCurve'),
          icon: 'mdi:chart-bell-curve',
        },
      },
      {
        name: 'PowerOperationReport',
        path: '/municipal-power/power-operation-report',
        component: () =>
          import('#/views/municipal_power/power_operation_report/index.vue'),
        meta: {
          title: $t('page.other.powerOperationReport'),
          icon: 'mdi:file-chart',
        },
      },
      {
        name: 'WiringDiagram',
        path: '/municipal-power/wiring-diagram',
        component: () =>
          import('#/views/municipal_power/wiring_diagram/index.vue'),
        meta: {
          title: $t('page.other.wiringDiagram'),
          icon: 'mdi:electric-switch',
        },
      },
      {
        name: 'MunicipalPowerDashboard',
        path: '/municipal-power/dashboard',
        component: () =>
          import('#/views/municipal_power/dashboard/index.vue'),
        meta: {
          title: $t('page.other.municipalPowerDashboard'),
          icon: 'mdi:view-dashboard',
        },
      },
      {
        name: 'DataMonitoring',
        path: '/municipal-power/data-monitoring',
        component: () =>
          import('#/views/municipal_power/data_monitoring/index.vue'),
        meta: {
          title: $t('page.other.dataMonitoring'),
          icon: 'mdi:monitor-dashboard',
        },
        children: [
          {
            name: 'DataMonitoringDetail',
            path: '/municipal-power/data-monitoring/detail',
            component: () =>
              import('#/views/municipal_power/data_monitoring/detail/index.vue'),
            meta: {
              title: $t('page.other.dataMonitoringDetail'),
              icon: 'mdi:monitor-eye',
              hideInMenu: false,
            },
          },
        ],
      },
      {
        name: 'GatewayStatus',
        path: '/municipal-power/gateway-status',
        component: () =>
          import('#/views/municipal_power/gateway_status/index.vue'),
        meta: {
          title: $t('page.other.gatewayStatus'),
          icon: 'mdi:router-network',
        },
      },
    ],
  },
];

export default routes;
