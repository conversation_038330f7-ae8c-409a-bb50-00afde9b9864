import { requestClient } from '#/api/request';

export namespace TreeNodeApi {
  export interface TreeNode {
    [key: string]: any;
    id: string;
    label: string;
    parentId?: string | null;
    level: number; // 节点层级：1-4
    type: 'group' | 'camera'; // 节点类型：分组或摄像头
    icon?: string;
    status?: number; // 状态：1-启用，0-禁用
    sort?: number; // 排序
    children?: TreeNode[];
    created_at?: string;
    updated_at?: string;
  }
}

// 树形节点数据类型定义
export type TreeNodeData = TreeNodeApi.TreeNode;

// 树形节点查询条件
export interface TreeNodeQuery {
  label?: string;
  type?: 'group' | 'camera';
  level?: number;
  parentId?: string;
  status?: number;
}

// 树形节点请求参数
export interface TreeNodeParams {
  query?: TreeNodeQuery;
}

// 树形节点响应数据
export interface TreeNodeResponse {
  items: TreeNodeData[];
  total?: number;
}

// 创建/更新节点的数据
export interface CreateTreeNodeData {
  label: string;
  parentId?: string | null;
  type: 'group' | 'camera';
  icon?: string;
  status?: number;
  sort?: number;
}

/**
 * 获取树形节点列表数据
 */
export const getTreeNodes = async (
  params?: TreeNodeParams,
): Promise<TreeNodeResponse> => {
  return requestClient.post<TreeNodeResponse>('/camera/tree-nodes', params || {});
};

/**
 * 创建树形节点
 * @param data 节点数据
 */
export const createTreeNode = async (data: CreateTreeNodeData) => {
  return requestClient.post('/camera/tree-node', data);
};

/**
 * 更新树形节点
 * @param id 节点 ID
 * @param data 节点数据
 */
export const updateTreeNode = async (
  id: string,
  data: Partial<CreateTreeNodeData>,
) => {
  return requestClient.put(`/camera/tree-node/${id}`, data);
};

/**
 * 删除树形节点
 * @param id 节点 ID
 */
export const deleteTreeNode = async (id: string) => {
  return requestClient.delete(`/camera/tree-node/${id}`);
};

/**
 * 获取单个树形节点详情
 * @param id 节点 ID
 */
export const getTreeNodeById = async (id: string): Promise<TreeNodeData> => {
  return requestClient.get<TreeNodeData>(`/camera/tree-node/${id}`);
};
