import { requestClient } from '#/api/request';

export namespace CameraApi {
  export interface CameraConfig {
    [key: string]: any;
    id: number | string;
    camera_name: string;
    brand: string;
    serial_number: string;
    video_token: string;
    video_key: string;
    video_secret: string;
    region: string;
  }
}

// 视频列表数据类型定义
export type CameraConfigData = CameraApi.CameraConfig;

// 视频列表查询条件
export interface VideoListQuery {
  camera_name?: string;
  brand?: string;
  serial_number?: string;
}

// 分页参数
export interface VideoListPagination {
  page?: number;
  pageSize?: number;
}

// 视频列表请求参数
export interface VideoListParams {
  query?: VideoListQuery;
  pagination?: VideoListPagination;
}

// 视频列表响应数据
export interface VideoListResponse {
  items: CameraConfigData[];
  total?: number;
  page?: number;
  pageSize?: number;
  totalPages?: number;
}

/**
 * 获取监控列表数据 (POST方法) 实际上传的是data不是params参数
 */
const getCameraList = async (
  params?: VideoListParams,
): Promise<VideoListResponse> => {
  return requestClient.post<VideoListResponse>('/camera/list', params);
};

/*
 * 创建监控
 * @param data 监控数据
 * */

const createCamera = async (data: Omit<CameraApi.CameraConfig, 'id'>) => {
  return requestClient.post('/camera/cameraConfig', data);
};

/*
 * 更新监控
 * @param id 监控 ID
 * @param data 监控数据
 * */
const updateCamera = async (
  id: number | string,
  data: Omit<CameraApi.CameraConfig, 'id'>,
) => {
  return requestClient.put(`/camera/cameraConfig/${id}`, data);
};

/**
 * 删除监控
 * @param id 监控 ID
 */
const deleteCamera = async (id: number | string) => {
  return requestClient.delete(`/camera/cameraConfig/${id}`);
};

export { createCamera, deleteCamera, getCameraList, updateCamera };
