# VXE Table 操作按钮渲染器迁移指南

## 📋 概述

本文档记录了将 VXE Table 操作按钮渲染器从 Ant Design Vue 迁移到 Element Plus 的完整过程，包括遇到的问题、解决方案和最佳实践。

## 🎯 迁移目标

- 将表格操作按钮从 Ant Design Vue 组件替换为 Element Plus 组件
- 保持原有功能完整性
- 确保样式和交互体验一致
- 消除控制台警告和错误

## 🔍 问题分析

### 1. 删除按钮样式问题

**问题描述**：
```typescript
// ❌ 错误：Element Plus 不支持 danger 属性
const presets = {
  delete: {
    danger: true,  // 这个属性在 Element Plus 中不存在
    text: $t('common.delete'),
  }
};
```

**根本原因**：
- Ant Design Vue 的 Button 组件使用 `danger` 属性表示危险按钮
- Element Plus 的 Button 组件使用 `type="danger"` 表示危险按钮

### 2. ElPopconfirm 事件处理问题

**问题描述**：
```typescript
// ❌ 错误：Element Plus 不支持 onOpenChange 事件
onOpenChange: (open: boolean) => {
  if (open) {
    viewportWrapper?.style.setProperty('pointer-events', 'none');
  } else {
    viewportWrapper?.style.removeProperty('pointer-events');
  }
}
```

**根本原因**：
- Ant Design Vue 的 Popconfirm 使用 `onOpenChange` 事件
- Element Plus 的 Popconfirm 使用 `onShow` 和 `onHide` 事件

### 3. 弹窗容器属性问题

**问题描述**：
```typescript
// ❌ 错误：Element Plus 不支持 getPopupContainer 属性
getPopupContainer(el) {
  viewportWrapper = el.closest('.vxe-table--viewport-wrapper');
  return document.body;
}
```

**根本原因**：
- Ant Design Vue 使用 `getPopupContainer` 控制弹窗挂载位置
- Element Plus 使用 `teleported` 属性控制是否挂载到 body

### 4. 按钮文本属性冲突问题

**问题描述**：
```typescript
// ❌ 错误：Element Plus Button 的 text 属性是 Boolean 类型
const presets = {
  delete: {
    type: 'danger',
    text: $t('common.delete'),  // 这里会导致类型错误
  }
};
```

**错误信息**：
```
[Vue warn]: Invalid prop: type check failed for prop "text". Expected Boolean, got String with value "修改".
```

**根本原因**：
- 在 Element Plus 中，ElButton 的 `text` 属性是 Boolean 类型，用于控制按钮是否为文本按钮样式
- 按钮的文本内容应该通过 slot 传递，而不是通过 `text` 属性

### 5. ElPopconfirm slot 结构问题

**问题描述**：
```
ElementPlusError: [ElOnlyChild] no valid child node found
```

**根本原因**：
- Element Plus 的 ElPopconfirm 组件需要正确的 slot 结构
- 触发元素应该放在 `reference` slot 中，而不是 `default` slot
- 弹窗内容应该放在 `default` slot 中

### 6. VXE Table 自定义渲染器组件迁移

#### ⚠️ 重要概念澄清

在 VXE Table 自定义渲染器中，需要区分两种不同的 `props`：

1. **VXE Table 渲染器的 `props`**：这是通过 `cellRender.props` 传递给渲染器的自定义属性
2. **Element Plus 组件的原生属性**：这是组件本身支持的属性

**示例说明**：
```typescript
// 表格列配置
{
  cellRender: {
    name: 'CellLink',
    props: { text: '编辑' }  // ← 这是 VXE 渲染器的自定义属性
  }
}

// 渲染器实现
vxeUI.renderer.add('CellLink', {
  renderTableDefault(renderOpts) {
    const { props } = renderOpts;  // ← 这里的 props 包含上面的 { text: '编辑' }
    return h(ElButton, {
      link: true,  // ← 这是 ElButton 的原生属性（Boolean 类型）
    }, {
      default: () => props?.text  // ← 使用 VXE 渲染器的自定义属性作为显示文本
    });
  },
});
```

#### 6.1 CellLink 组件问题

**问题描述**：
- 容易混淆 VXE Table 渲染器的 `props.text`（自定义属性）和 ElButton 的 `text` 属性（Boolean 类型）
- ElButton 的 `text` 属性是 Boolean 类型，用于控制是否为文本按钮样式
- VXE Table 渲染器的 `props.text` 是自定义属性，用于传递按钮显示的文本内容

#### 6.2 CellTag 组件迁移

**问题描述**：
- 使用了 Ant Design Vue 的 `Tag` 组件和属性
- `color` 属性在 Element Plus 中应该是 `type`

#### 6.3 CellSwitch 组件迁移

**问题描述**：
- 使用了 Ant Design Vue 的 `Switch` 组件和属性
- 事件处理和属性名称需要适配 Element Plus

## 🔧 解决方案

### 1. 修复删除按钮样式

**修复前**：
```typescript
const presets: Recordable<Recordable<any>> = {
  delete: {
    danger: true,  // ❌ 错误属性
    text: $t('common.delete'),
  },
  edit: {
    text: $t('common.edit'),
  },
};
```

**修复后**：
```typescript
const presets: Recordable<Recordable<any>> = {
  delete: {
    type: 'danger',  // ✅ 正确：使用 type 属性
    text: $t('common.delete'),
  },
  edit: {
    text: $t('common.edit'),
  },
};
```

### 2. 修复 ElPopconfirm 事件处理

**修复前**：
```typescript
onOpenChange: (open: boolean) => {
  // 当弹窗打开时，禁止表格的滚动
  if (open) {
    viewportWrapper?.style.setProperty('pointer-events', 'none');
  } else {
    viewportWrapper?.style.removeProperty('pointer-events');
  }
},
```

**修复后**：
```typescript
onShow: () => {
  // 当弹窗打开时，禁止表格的滚动
  const viewportWrapper = document.querySelector('.vxe-table--viewport-wrapper') as HTMLElement;
  viewportWrapper?.style.setProperty('pointer-events', 'none');
},
onHide: () => {
  // 当弹窗关闭时，恢复表格的滚动
  const viewportWrapper = document.querySelector('.vxe-table--viewport-wrapper') as HTMLElement;
  viewportWrapper?.style.removeProperty('pointer-events');
},
```

### 3. 修复弹窗容器属性

**修复前**：
```typescript
getPopupContainer(el) {
  viewportWrapper = el.closest('.vxe-table--viewport-wrapper');
  return document.body;
},
placement: 'topLeft',  // ❌ 错误的 placement 值
```

**修复后**：
```typescript
teleported: true,  // ✅ 使用 teleported 属性
placement: 'top-start',  // ✅ 使用正确的 placement 值
```

### 4. 修复按钮文本属性冲突

**修复前**：
```typescript
const presets: Recordable<Recordable<any>> = {
  delete: {
    type: 'danger',
    text: $t('common.delete'),  // ❌ 错误：text 属性冲突
  },
  edit: {
    text: $t('common.edit'),  // ❌ 错误：text 属性冲突
  },
};

function renderBtn(opt: Recordable<any>, listen = true) {
  return h(ElButton, {
    ...props,
    ...opt,  // ❌ 这里会将 text 字符串传给 ElButton
  }, {
    default: () => {
      content.push(opt.text);  // ❌ 使用了冲突的 text 属性
      return content;
    },
  });
}
```

**修复后**：
```typescript
const presets: Recordable<Recordable<any>> = {
  delete: {
    type: 'danger',
    buttonText: $t('common.delete'),  // ✅ 使用 buttonText 避免冲突
  },
  edit: {
    buttonText: $t('common.edit'),  // ✅ 使用 buttonText 避免冲突
  },
};

function renderBtn(opt: Recordable<any>, listen = true) {
  // ✅ 分离按钮文本和按钮属性
  const { buttonText, ...buttonProps } = opt;

  return h(ElButton, {
    ...props,
    ...buttonProps,  // ✅ 只传递按钮属性，不包含文本
  }, {
    default: () => {
      content.push(buttonText || opt.text);  // ✅ 使用分离的文本内容
      return content;
    },
  });
}
```

### 5. 修复 ElPopconfirm slot 结构

**修复前**：
```typescript
// ❌ 错误：slot 结构不正确
h(ElPopconfirm, {
  // ... 属性
}, {
  default: () => renderBtn({ ...opt }, false),  // ❌ 触发元素放在了 default slot
  description: () => h('div', { class: 'truncate' }, '确认信息'),  // ❌ 使用了不存在的 description slot
});
```

**修复后**：
```typescript
// ✅ 正确：使用正确的 slot 结构
h(ElPopconfirm, {
  // ... 属性
}, {
  reference: () => renderBtn({ ...opt }, false),  // ✅ 触发元素放在 reference slot
  default: () => h('div', { class: 'truncate' }, '确认信息'),  // ✅ 弹窗内容放在 default slot
});
```

### 6. 修复 VXE Table 自定义渲染器

#### 6.1 修复 CellLink 组件

**修复前**：
```typescript
// ❌ 概念混淆：没有区分 VXE 渲染器属性和 ElButton 原生属性
vxeUI.renderer.add('CellLink', {
  renderTableDefault(renderOpts) {
    const { props } = renderOpts;
    return h(ElButton, {
      size: 'small',
      text: true,  // ❌ 错误：这是 ElButton 的 Boolean 属性
    }, { default: () => props?.text });
  },
});
```

**修复后**：
```typescript
// ✅ 正确区分：VXE 渲染器的 props.text 是自定义属性，用于显示文本
vxeUI.renderer.add('CellLink', {
  renderTableDefault(renderOpts) {
    const { props } = renderOpts;
    // props.text 是传递给渲染器的自定义属性，用于显示按钮文本
    // 不要与 ElButton 的 text 属性（Boolean 类型）混淆
    return h(ElButton, {
      size: 'small',
      link: true,  // ✅ 使用 link: true 创建链接样式按钮
    }, { default: () => props?.text || '链接' });
  },
});
```

**使用方式**：
```typescript
// 在表格列配置中使用
{
  field: 'action',
  title: '操作',
  cellRender: {
    name: 'CellLink',
    props: { text: '编辑' }  // 这里的 text 是自定义属性
  },
}
```

#### 6.2 修复 CellTag 组件

**修复前**：
```typescript
// ❌ 使用 Ant Design Vue 的 Tag 组件和属性
vxeUI.renderer.add('CellTag', {
  renderTableDefault({ options, props }, { column, row }) {
    const tagOptions = options ?? [
      { color: 'success', label: $t('common.enabled'), value: 1 },  // ❌ color 属性
      { color: 'error', label: $t('common.disabled'), value: 0 },
    ];
    return h(Tag, {  // ❌ 未定义的 Tag 组件
      ...props,
      ...objectOmit(tagItem ?? {}, ['label']),  // ❌ objectOmit 未导入
    }, { default: () => tagItem?.label ?? value });
  },
});
```

**修复后**：
```typescript
// ✅ 使用 Element Plus 的 ElTag 组件和属性
vxeUI.renderer.add('CellTag', {
  renderTableDefault({ options, props }, { column, row }) {
    const tagOptions = options ?? [
      { type: 'success', label: $t('common.enabled'), value: 1 },  // ✅ type 属性
      { type: 'danger', label: $t('common.disabled'), value: 0 },
    ];
    return h(ElTag, {  // ✅ 使用 ElTag
      ...props,
      type: tagItem?.type || 'info',  // ✅ 使用 type 属性
      size: props?.size || 'small',
    }, { default: () => tagItem?.label ?? value });
  },
});
```

#### 6.3 修复 CellSwitch 组件

**修复前**：
```typescript
// ❌ 使用 Ant Design Vue 的 Switch 组件和属性
vxeUI.renderer.add('CellSwitch', {
  renderTableDefault({ attrs, props }, { column, row }) {
    const finallyProps = {
      checkedChildren: $t('common.enabled'),    // ❌ Ant Design 属性
      checkedValue: 1,                          // ❌ Ant Design 属性
      unCheckedChildren: $t('common.disabled'), // ❌ Ant Design 属性
      unCheckedValue: 0,                        // ❌ Ant Design 属性
      checked: row[column.field],               // ❌ Ant Design 属性
      'onUpdate:checked': onChange,             // ❌ Ant Design 事件
    };
    return h(Switch, finallyProps);  // ❌ 未定义的 Switch 组件
  },
});
```

**修复后**：
```typescript
// ✅ 使用 Element Plus 的 ElSwitch 组件和属性
vxeUI.renderer.add('CellSwitch', {
  renderTableDefault({ attrs, props }, { column, row }) {
    const finallyProps = {
      ...props,
      modelValue: row[column.field] === 1,      // ✅ Element Plus 属性
      activeValue: 1,                           // ✅ Element Plus 属性
      inactiveValue: 0,                         // ✅ Element Plus 属性
      activeText: $t('common.enabled'),         // ✅ Element Plus 属性
      inactiveText: $t('common.disabled'),      // ✅ Element Plus 属性
      'onUpdate:modelValue': onChange,          // ✅ Element Plus 事件
    };
    async function onChange(newVal: boolean) {
      const switchValue = newVal ? 1 : 0;  // ✅ 转换 Boolean 到数值
      // ... 其他逻辑
    }
    return h(ElSwitch, finallyProps);  // ✅ 使用 ElSwitch
  },
});
```

## 📝 完整的修复代码

```typescript
function renderConfirm(opt: Recordable<any>) {
  return h(
    ElPopconfirm,
    {
      /**
       * Element Plus ElPopconfirm 使用 teleported 属性控制是否挂载到 body
       * 当popconfirm用在固定列中时，将固定列作为弹窗的容器时可能会因为固定列较窄而无法容纳弹窗
       * 将表格主体区域作为弹窗容器时又会因为固定列的层级较高而遮挡弹窗
       * 将body或者表格视口区域作为弹窗容器时又会导致弹窗无法跟随表格滚动。
       * 鉴于以上各种情况，一种折中的解决方案是弹出层展示时，禁止操作表格的滚动条。
       * 这样既解决了弹窗的遮挡问题，又不至于让弹窗随着表格的滚动而跑出视口区域。
       */
      teleported: true,
      placement: 'top-start',
      title: $t('ui.actionTitle.delete', [attrs?.nameTitle || '']),
      ...props,
      ...opt,
      icon: undefined,
      onShow: () => {
        // 当弹窗打开时，禁止表格的滚动
        const viewportWrapper = document.querySelector('.vxe-table--viewport-wrapper') as HTMLElement;
        viewportWrapper?.style.setProperty('pointer-events', 'none');
      },
      onHide: () => {
        // 当弹窗关闭时，恢复表格的滚动
        const viewportWrapper = document.querySelector('.vxe-table--viewport-wrapper') as HTMLElement;
        viewportWrapper?.style.removeProperty('pointer-events');
      },
      onConfirm: () => {
        attrs?.onClick?.({
          code: opt.code,
          row,
        });
      },
    },
    {
      default: () => renderBtn({ ...opt }, false),
      description: () =>
        h(
          'div',
          { class: 'truncate' },
          $t('ui.actionMessage.deleteConfirm', [
            row[attrs?.nameField || 'name'],
          ]),
        ),
    },
  );
}
```

## 🔄 组件属性对照表

| 功能 | Ant Design Vue | Element Plus | 说明 |
|------|----------------|--------------|------|
| 危险按钮 | `danger: true` | `type: 'danger'` | 按钮样式属性 |
| 按钮文本 | `text: '文本'` | `buttonText: '文本'` | 按钮文本内容，避免与 text 属性冲突 |
| 弹窗显示事件 | `onOpenChange` | `onShow` | 弹窗打开时触发 |
| 弹窗隐藏事件 | `onOpenChange` | `onHide` | 弹窗关闭时触发 |
| 弹窗容器 | `getPopupContainer` | `teleported: true` | 控制弹窗挂载位置 |
| 弹窗位置 | `topLeft` | `top-start` | 弹窗显示位置 |
| 触发元素 slot | `default` | `reference` | 触发弹窗的元素 |
| 弹窗内容 slot | `content` | `default` | 弹窗显示的内容 |
| Tag 颜色属性 | `color: 'success'` | `type: 'success'` | 标签颜色/类型 |
| Switch 选中值 | `checked: true` | `modelValue: true` | 开关状态 |
| Switch 选中事件 | `onUpdate:checked` | `onUpdate:modelValue` | 开关状态变化事件 |
| Switch 选中文本 | `checkedChildren` | `activeText` | 开关打开时的文本 |
| Switch 未选中文本 | `unCheckedChildren` | `inactiveText` | 开关关闭时的文本 |

## ✅ 验证结果

修复完成后，确保以下功能正常：

1. **删除按钮样式**：显示为红色危险按钮样式
2. **弹窗交互**：点击删除按钮正常显示确认弹窗
3. **滚动控制**：弹窗显示时表格滚动被禁用，关闭后恢复
4. **弹窗位置**：弹窗正确挂载到 body，避免层级问题
5. **控制台清洁**：无相关警告或错误信息

## 🎯 最佳实践

### 1. 事件处理分离
将单一的 `onOpenChange` 事件分离为 `onShow` 和 `onHide`，逻辑更清晰：

```typescript
// ✅ 推荐：逻辑分离，职责单一
onShow: () => {
  // 只处理显示时的逻辑
},
onHide: () => {
  // 只处理隐藏时的逻辑
},
```

### 2. DOM 查询优化
使用 `document.querySelector` 替代闭包变量，避免内存泄漏：

```typescript
// ✅ 推荐：每次查询，避免引用过期的 DOM
const viewportWrapper = document.querySelector('.vxe-table--viewport-wrapper') as HTMLElement;
```

### 3. 属性映射标准化
建立组件属性对照表，确保迁移的一致性和准确性。

## 📚 相关文档

- [Element Plus Button 组件文档](https://element-plus.org/en-US/component/button)
- [Element Plus Popconfirm 组件文档](https://element-plus.org/en-US/component/popconfirm)
- [VXE Table 自定义渲染器文档](https://vxetable.cn/#/table/renderer/default)

## 🔍 故障排除

### 常见问题

1. **按钮样式不正确**
   - 检查是否使用了 `type: 'danger'` 而不是 `danger: true`

2. **按钮文本属性类型错误**
   - 错误：`[Vue warn]: Invalid prop: type check failed for prop "text". Expected Boolean, got String`
   - 解决：使用 `buttonText` 属性存储文本内容，避免与 ElButton 的 `text` 属性冲突

3. **ElPopconfirm 子节点错误**
   - 错误：`ElementPlusError: [ElOnlyChild] no valid child node found`
   - 解决：使用正确的 slot 结构，触发元素放在 `reference` slot，弹窗内容放在 `default` slot

4. **弹窗事件不触发**
   - 确认使用了 `onShow` 和 `onHide` 而不是 `onOpenChange`

4. **弹窗位置异常**
   - 检查 `teleported: true` 属性是否设置
   - 确认 `placement` 值使用了正确的格式

5. **控制台警告**
   - 检查是否移除了不支持的属性（如 `getPopupContainer`）
   - 确认没有将字符串类型的值传递给 Boolean 类型的属性
   - 确认 ElPopconfirm 使用了正确的 slot 结构

6. **VXE Table 自定义渲染器问题**
   - **概念混淆**：区分 VXE 渲染器的 `props`（自定义属性）和 Element Plus 组件的原生属性
   - **CellLink 显示空白**：确认 `props?.text` 有默认值，且没有将其作为 ElButton 的原生属性传递
   - **CellTag 样式不正确**：检查是否使用了 `type` 而不是 `color` 属性
   - **CellSwitch 不工作**：确认使用了 `modelValue` 和 `onUpdate:modelValue`
   - **组件未定义错误**：确认已导入 `ElTag` 和 `ElSwitch` 组件
   - **属性类型错误**：确认没有将字符串类型的自定义属性传递给 Boolean 类型的组件属性

---

**文档版本**：v1.0  
**更新时间**：2025-01-28  
**维护者**：开发团队
