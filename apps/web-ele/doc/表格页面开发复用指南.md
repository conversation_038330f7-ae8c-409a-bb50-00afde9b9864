# 表格页面开发复用指南

基于企业收益报表页面的开发经验，本文档总结了一套可复用的表格页面开发步骤和模板，适用于类似的数据展示页面开发。

## 目录
- [项目结构规划](#项目结构规划)
- [开发步骤详解](#开发步骤详解)
- [代码模板](#代码模板)
- [快速复用检查清单](#快速复用检查清单)
- [关键复用要点](#关键复用要点)

## 项目结构规划

### 文件组织结构
```
src/views/[模块名]/[页面名]/
├── index.vue          # 主页面组件
├── data.ts           # 表格列配置和数据处理
└── types.ts          # 类型定义（可选）

src/api/[模块名]/
└── [页面名].ts       # API接口定义

apps/backend-mock/api/[模块名]/
└── [页面名].get.ts   # Mock API数据

src/locales/langs/
├── zh-CN/system.json # 中文国际化
└── en-US/system.json # 英文国际化
```

## 开发步骤详解

### 第一步：数据类型定义

**文件位置**: `src/api/[模块名]/[页面名].ts`

定义数据接口和查询参数，确保前后端类型一致。

### 第二步：国际化配置

**文件位置**: `src/locales/langs/zh-CN/system.json` 和 `src/locales/langs/en-US/system.json`

添加页面相关的多语言支持，包括标题、列名、单位等。

### 第三步：Mock API 数据生成

**文件位置**: `apps/backend-mock/api/[模块名]/[页面名].get.ts`

创建模拟数据生成器，支持动态时间范围和报表类型。

### 第四步：表格列配置

**文件位置**: `src/views/[模块名]/[页面名]/data.ts`

配置表格列结构，包括分组列、子列、合计列等。

### 第五步：主页面组件

**文件位置**: `src/views/[模块名]/[页面名]/index.vue`

创建主页面组件，集成表格、搜索、标签页等功能。

### 第六步：路由配置

在相应的路由文件中添加页面路由配置。

## 代码模板

### 数据类型定义模板

```typescript
// src/api/[模块名]/[页面名].ts
export interface [PageName]Data {
  time: string; // 时间字段（必需）
  // 根据实际需求添加字段
  [fieldName]: number; // 数值字段
  [fieldName]Amount: number; // 数量字段
  // ...其他字段
}

export interface [PageName]Params {
  startTime?: string;
  endTime?: string;
  reportType?: 'monthly' | 'yearly';
  // ...其他查询参数
}
```

### 国际化配置模板

```json
{
  "[pageName]": {
    "title": "页面标题",
    "description": "页面描述",
    "time": "时间",
    "date": "日期", 
    "month": "月份",
    "[columnName]": "列名",
    "[subColumnName]": "子列名(单位)",
    "income": "收益(元)",
    "amount": "数量(单位)",
    "total": "合计",
    "footer": {
      "total": "合计"
    }
  }
}
```

### Mock API 模板

```typescript
// apps/backend-mock/api/[模块名]/[页面名].get.ts
import { faker } from '@faker-js/faker/locale/zh_CN';

function generateData(): [PageName]Data {
  return {
    time: faker.date.recent().toISOString().split('T')[0],
    [fieldName]: Number(faker.number.float({ 
      min: 最小值, 
      max: 最大值, 
      fractionDigits: 2 
    })),
    [fieldName]Amount: Number(faker.number.float({ 
      min: 最小值, 
      max: 最大值, 
      fractionDigits: 1 
    })),
  };
}

export default defineMockHandler({
  url: '/api/[module]/[page]',
  method: 'GET',
  handler: (event) => {
    const query = getQuery(event);
    const data = generateDynamicMockData(
      query.startTime as string,
      query.endTime as string,
      query.reportType as 'monthly' | 'yearly'
    );
    return useResponseSuccess(data);
  },
});
```

### 表格列配置模板

```typescript
// src/views/[模块名]/[页面名]/data.ts
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { $t } from '#/locales';

export function useColumns(
  reportType: ReportType = 'monthly',
  onActionClick?: OnActionClickFn<[PageName]Data>,
): VxeTableGridOptions<[PageName]Data>['columns'] {
  return [
    // 1. 时间列（固定模式）
    {
      align: 'center',
      field: 'time',
      fixed: 'left',
      title: reportType === 'monthly'
        ? $t('system.[pageName].date')
        : $t('system.[pageName].month'),
      width: 120,
    },

    // 2. 业务列组（根据需求调整）
    {
      align: 'center',
      title: $t('system.[pageName].[columnName]'),
      children: [
        {
          align: 'center',
          field: '[fieldName]',
          title: $t('system.[pageName].[subColumnName]'),
          width: 120,
        },
        // 更多子列...
      ],
    },

    // 3. 合计列（如果需要）
    {
      align: 'center',
      title: $t('system.[pageName].total'),
      children: [
        {
          align: 'center',
          field: 'calculatedTotal',
          title: $t('system.[pageName].totalAmount'),
          width: 120,
          formatter: ({ row }: { row: any }) => {
            const total = (
              (Number(row.[field1]) || 0) +
              (Number(row.[field2]) || 0) +
              // ...更多字段
            );
            return total.toFixed(2);
          },
        },
      ],
    },
  ];
}
```

### 主页面组件模板

```vue
<template>
  <div class="p-4">
    <PageWrapper
      :title="$t('system.[pageName].title')"
      :description="$t('system.[pageName].description')"
    >
      <!-- 标签页（如果需要） -->
      <ElTabs v-model="activeTab" @tab-change="handleTabChange">
        <ElTabPane label="月报" name="monthly" />
        <ElTabPane label="年报" name="yearly" />
      </ElTabs>

      <!-- 表格组件 -->
      <VxeTableGrid v-bind="gridOptions" class="mt-4" />
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useVbenVxeTable } from '#/adapter/vxe-table';
import { useColumns } from './data';
import { get[PageName]List } from '#/api/[module]/[page]';

const activeTab = ref<'monthly' | 'yearly'>('monthly');

const gridOptions = useVbenVxeTable({
  columns: computed(() => useColumns(activeTab.value)),
  proxyConfig: {
    ajax: {
      query: async ({ page, form }) => {
        return get[PageName]List({
          ...form,
          reportType: activeTab.value,
          page: page.currentPage,
          pageSize: page.pageSize,
        });
      },
    },
  },
  footerMethod: ({ data }) => calculateFooterData(data),
});

function calculateFooterData(data: [PageName]Data[]): [PageName]Data {
  const footerData: any = {
    time: $t('system.[pageName].footer.total'),
  };

  if (data.length === 0) return footerData;

  Object.keys(data[0]).forEach((key) => {
    if (key !== 'time') {
      const total = data.reduce((sum, row) => {
        const value = Number(row[key as keyof [PageName]Data]) || 0;
        return sum + value;
      }, 0);
      footerData[key] = Number(total.toFixed(2));
    }
  });

  return footerData;
}

function handleTabChange() {
  gridOptions.commitProxy('query');
}
</script>
```

## 快速复用检查清单

### ✅ 开发前准备
- [ ] 确定页面名称和模块名称
- [ ] 分析表格列结构和数据字段
- [ ] 确定是否需要标签页（月报/年报）
- [ ] 确定是否需要合计列

### ✅ 文件创建清单
- [ ] 创建页面目录结构
- [ ] 定义数据类型接口
- [ ] 添加国际化配置
- [ ] 创建Mock API
- [ ] 配置表格列
- [ ] 创建主页面组件
- [ ] 添加路由配置

### ✅ 功能验证清单
- [ ] 数据加载正常
- [ ] 表格显示正确
- [ ] 搜索功能正常
- [ ] 导出功能正常
- [ ] 标签页切换正常（如果有）
- [ ] 合计计算正确（如果有）
- [ ] 表尾合计正确

## 关键复用要点

### 1. 命名规范
- **页面名称**：使用PascalCase，如`EnterpriseIncomeReport`
- **字段名称**：使用camelCase，如`solarIncome`
- **国际化key**：使用点分隔，如`system.incomeReport.title`

### 2. 类型定义复用
- 前后端使用相同的接口定义
- 保持字段名称和类型的一致性
- 合理使用可选字段和联合类型

### 3. 数据生成策略
- 使用faker.js生成符合业务逻辑的测试数据
- 根据字段类型设置合适的数值范围
- 支持动态时间范围和报表类型

### 4. 表格配置模式
- 时间列固定在左侧
- 业务列使用分组结构
- 合计列使用formatter进行实时计算
- 保持列宽和对齐方式的一致性
- 使用minWidth替代固定width实现自适应布局
- 配置表格自适应选项填充可用空间

### 5. 计算逻辑复用
- 合计列：使用formatter进行行内计算
- 表尾合计：在footerMethod中进行列总计
- 数值精度：统一保留2位小数
- 容错处理：对空值进行0值处理

### 6. 性能优化
- 使用computed响应式计算列配置
- 合理使用防抖和节流
- 避免在渲染函数中进行复杂计算

## 常见问题解决

### Q: 合计列计算不准确？
**A**: 检查字段名是否匹配，确保使用Number()转换，处理空值情况。

### Q: 国际化不生效？
**A**: 检查国际化key是否正确，确保在两种语言文件中都有定义。

### Q: Mock数据不符合预期？
**A**: 检查faker.js的配置参数，确保数值范围和精度设置正确。

### Q: 表格列显示异常？
**A**: 检查列配置的field字段是否与数据字段匹配，确保width设置合理。

### Q: 表格不能填充满水平空间？
**A**: 使用以下配置实现表格自适应宽度：

```typescript
// 1. 表格配置中添加自适应选项
const createGridOptions = (reportType: ReportType) => ({
  // 表格自适应配置
  resizable: true, // 允许调整列宽
  autoResize: true, // 自动调整大小
  columnConfig: {
    resizable: true, // 列可调整大小
    useKey: true, // 使用key优化性能
  },
  // 其他配置...
});

// 2. 列配置中使用minWidth替代固定width
{
  align: 'center',
  field: 'fieldName',
  title: 'Column Title',
  minWidth: 120, // 使用minWidth允许自适应
}

// 3. 添加CSS样式确保表格填充容器
:deep(.vxe-table) {
  width: 100% !important;
  min-width: 100%;

  .vxe-table--main-wrapper,
  .vxe-table--body-wrapper,
  .vxe-table--header-wrapper {
    width: 100% !important;
  }
}
```

---

**注意**: 本指南基于Vben Admin框架和VXE Table组件，使用时请根据实际项目情况进行调整。
