# 大屏可视化设计规范 (1920*1080)

## 1. 基础尺寸设置

### 设计稿尺寸
- **宽度**: 1920px
- **高度**: 1080px
- **比例**: 16:9

### autofit.js 配置
```javascript
autofit.init({
  dh: 1080,  // 设计稿高度
  dw: 1920,  // 设计稿宽度
  el: '#root-visualizer-container',
  resize: true,
});
```

## 2. 布局规范

### 整体布局建议
```
┌─────────────────────────────────────────────────────────────┐
│                    Header (1920 x 80-120)                  │
├─────────────────────────────────────────────────────────────┤
│  Left    │                                    │    Right   │
│ Panel    │           Main Content             │   Panel    │
│(300-400) │            (1120-1320)             │ (300-400)  │
│          │                                    │            │
│          │                                    │            │
├─────────────────────────────────────────────────────────────┤
│                   Footer (1920 x 60-80)                    │
└─────────────────────────────────────────────────────────────┘
```

### 推荐尺寸
- **顶部标题区**: 1920 x 100px
- **左侧面板**: 350 x 880px
- **中间内容区**: 1220 x 880px  
- **右侧面板**: 350 x 880px
- **底部状态栏**: 1920 x 80px

## 3. 字体规范

### 字体大小
- **主标题**: 36-48px
- **副标题**: 24-32px
- **正文**: 16-20px
- **小字**: 12-14px
- **数值显示**: 28-36px

### 字体颜色
```scss
// 主要文字颜色
$text-primary: #ffffff;
$text-secondary: #b3c5ef;
$text-muted: #7a8db8;

// 强调色
$accent-blue: #00d4ff;
$accent-green: #00ff88;
$accent-orange: #ff8c00;
$accent-red: #ff4757;
```

## 4. 组件尺寸规范

### 卡片组件
```scss
.dashboard-card {
  min-height: 200px;
  border-radius: 8px;
  padding: 20px;
  margin: 10px;
}

// 小卡片
.card-small { width: 280px; height: 180px; }
// 中等卡片  
.card-medium { width: 380px; height: 240px; }
// 大卡片
.card-large { width: 580px; height: 360px; }
```

### 图表组件
```scss
// ECharts 容器建议尺寸
.chart-small { width: 300px; height: 200px; }
.chart-medium { width: 500px; height: 300px; }
.chart-large { width: 800px; height: 400px; }
.chart-full { width: 100%; height: 100%; }
```

## 5. 间距规范

### 标准间距
```scss
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;
```

### 网格系统
- **栅格**: 24栅格系统
- **间隙**: 16px
- **容器边距**: 20px

## 6. 颜色规范

### 背景色
```scss
$bg-primary: #0a0e27;
$bg-secondary: #1a1f3a;
$bg-card: rgba(255, 255, 255, 0.05);
$bg-hover: rgba(255, 255, 255, 0.1);
```

### 边框色
```scss
$border-primary: rgba(255, 255, 255, 0.1);
$border-secondary: rgba(255, 255, 255, 0.05);
$border-accent: #00d4ff;
```

## 7. 响应式适配

### 不同屏幕尺寸适配
```javascript
// 4K屏幕 (3840x2160)
if (window.innerWidth >= 3840) {
  autofit.init({ dw: 1920, dh: 1080, resize: true });
}

// 2K屏幕 (2560x1440) 
if (window.innerWidth >= 2560) {
  autofit.init({ dw: 1920, dh: 1080, resize: true });
}

// 标准1080P (1920x1080)
if (window.innerWidth >= 1920) {
  autofit.init({ dw: 1920, dh: 1080, resize: true });
}
```

## 8. 性能优化建议

### 图片资源
- 背景图片建议使用 WebP 格式
- 图标使用 SVG 或 IconFont
- 大图片进行懒加载

### 动画效果
```scss
// 标准过渡动画
.transition-standard {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 数据更新动画
.data-update {
  animation: dataFlash 0.5s ease-in-out;
}

@keyframes dataFlash {
  0% { opacity: 1; }
  50% { opacity: 0.6; background-color: #00d4ff; }
  100% { opacity: 1; }
}
```

## 9. 开发工具类

### 常用 CSS 类
```scss
// 布局
.flex-center { display: flex; justify-content: center; align-items: center; }
.flex-between { display: flex; justify-content: space-between; align-items: center; }
.flex-column { display: flex; flex-direction: column; }

// 文字
.text-center { text-align: center; }
.text-ellipsis { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }

// 边距
.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mt-16 { margin-top: 16px; }
.mb-16 { margin-bottom: 16px; }
```

## 10. 使用示例

### 基础页面结构
```vue
<template>
  <div id="root-visualizer-container">
    <div id="visualizer-container">
      <!-- 顶部标题 -->
      <header class="dashboard-header">
        <h1 class="main-title">大屏可视化平台</h1>
      </header>
      
      <!-- 主要内容区 -->
      <main class="dashboard-main">
        <aside class="left-panel">
          <!-- 左侧面板内容 -->
        </aside>
        
        <section class="center-content">
          <!-- 中间主要内容 -->
        </section>
        
        <aside class="right-panel">
          <!-- 右侧面板内容 -->
        </aside>
      </main>
      
      <!-- 底部状态栏 -->
      <footer class="dashboard-footer">
        <!-- 底部内容 -->
      </footer>
    </div>
  </div>
</template>
```

这个规范文件可以作为团队开发的参考标准，确保大屏项目的一致性和专业性。
