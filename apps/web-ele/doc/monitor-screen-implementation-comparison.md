# 监控画面实现方式对比文档

## 📋 概述

本文档详细对比了监控画面与树形节点关联的两种不同实现方式，帮助理解设计思路的差异和各自的适用场景。

## 🔄 实现方式对比

### 方式一：动态分配模式（初始实现）

#### 核心思路
- **动态获取**：从树形数据中提取所有摄像头节点
- **自动分配**：将前4个摄像头自动分配到4个监控画面
- **灵活切换**：用户可以将任意摄像头切换到任意画面

#### 数据结构
```typescript
// 动态监控画面数据
const currentMonitorScreens = ref<Array<{
  id: string;
  title: string;
  status: 'online' | 'offline' | 'connecting';
}>>([]);

// 提取所有摄像头节点
const extractCameraNodes = (nodes: TreeNodeData[]): TreeNodeData[] => {
  // 递归遍历，收集所有 type === 'camera' 的节点
};

// 自动分配逻辑
const updateMonitorScreens = () => {
  const cameraNodes = extractCameraNodes(treeData.value);
  // 取前4个摄像头作为监控画面
  currentMonitorScreens.value = Array.from({ length: 4 }, (_, index) => {
    const camera = cameraNodes[index];
    return camera ? {
      id: camera.id,
      title: camera.label,
      status: getRandomStatus(),
    } : {
      id: `empty-${index}`,
      title: `监控画面 ${index + 1}`,
      status: 'offline',
    };
  });
};
```

#### 交互逻辑
- **左键点击摄像头** → 自动切换到监控画面1
- **右键菜单** → 选择切换到指定监控画面
- **画面内容** → 基于当前分配的摄像头动态显示

#### 优点
✅ **灵活性高**：可以显示任意数量的摄像头  
✅ **自动适配**：摄像头数量变化时自动调整  
✅ **用户控制**：用户可以自由组合监控画面  
✅ **扩展性强**：易于支持更多监控画面  

#### 缺点
❌ **不确定性**：每次加载可能显示不同的摄像头  
❌ **配置丢失**：刷新页面后用户配置会重置  
❌ **业务逻辑复杂**：需要处理动态分配和切换逻辑  

---

### 方式二：固定映射模式（调整后实现）

#### 核心思路
- **固定配置**：预定义4个监控画面对应的摄像头节点ID
- **一对一映射**：每个监控画面固定对应一个特定的摄像头节点
- **精确查找**：根据节点ID精确查找对应的摄像头

#### 数据结构
```typescript
// 固定的四个监控画面配置
const monitorScreenConfig = [
  { cameraId: '1-1-1-1-1', defaultTitle: '监控画面 1' }, // N-D2-5-1(纸箱打包机)
  { cameraId: '1-1-1-2-1', defaultTitle: '监控画面 2' }, // N-D2-5-2(包装生产线)
  { cameraId: '1-1-1-2-2', defaultTitle: '监控画面 3' }, // N-D2-5-3(质检设备)
  { cameraId: '1-1-2-1-1', defaultTitle: '监控画面 4' }, // N-D3-1-1(生产线A)
];

// 监控画面数据（包含摄像头节点引用）
const currentMonitorScreens = ref<Array<{
  id: string;
  title: string;
  status: 'online' | 'offline' | 'connecting';
  cameraNode?: TreeNodeData | null;
}>>([]);

// 精确查找指定ID的摄像头节点
const findCameraNodeById = (nodes: TreeNodeData[], targetId: string): TreeNodeData | null => {
  // 递归查找指定ID的节点
};

// 固定映射逻辑
const updateMonitorScreens = () => {
  currentMonitorScreens.value = monitorScreenConfig.map((config, index) => {
    const cameraNode = findCameraNodeById(treeData.value, config.cameraId);
    return cameraNode ? {
      id: cameraNode.id,
      title: cameraNode.label,
      status: getRandomStatus(),
      cameraNode: cameraNode,
    } : {
      id: `empty-${index}`,
      title: config.defaultTitle,
      status: 'offline',
      cameraNode: null,
    };
  });
};
```

#### 交互逻辑
- **左键点击摄像头** → 显示该摄像头对应的监控画面编号
- **右键菜单** → 可以重新分配摄像头到指定画面（更新配置）
- **画面内容** → 基于固定配置显示对应的摄像头

#### 优点
✅ **确定性强**：每次加载显示相同的摄像头配置  
✅ **业务明确**：每个画面有明确的业务含义  
✅ **配置持久**：可以保存和恢复用户配置  
✅ **性能更好**：精确查找，无需遍历所有节点  

#### 缺点
❌ **灵活性低**：只能显示预定义的摄像头  
❌ **配置依赖**：需要预先知道摄像头节点ID  
❌ **扩展复杂**：增加监控画面需要修改配置  

## 📊 详细对比表

| 对比维度 | 动态分配模式 | 固定映射模式 |
|---------|-------------|-------------|
| **数据获取** | 遍历提取所有摄像头 | 根据ID精确查找 |
| **画面分配** | 自动分配前N个摄像头 | 固定配置一对一映射 |
| **用户体验** | 灵活但不确定 | 确定但相对固定 |
| **配置持久化** | 不支持（刷新重置） | 支持（可保存配置） |
| **业务场景** | 通用监控系统 | 专业监控中心 |
| **扩展性** | 易于扩展画面数量 | 需要修改配置代码 |
| **性能** | 需要遍历所有节点 | 精确查找，性能更好 |
| **维护性** | 逻辑相对复杂 | 配置清晰，易于维护 |

## 🎯 适用场景分析

### 动态分配模式适用于：

#### 1. **通用监控系统**
- 摄像头数量和类型经常变化
- 用户需要灵活组合监控画面
- 支持多种监控场景切换

#### 2. **演示和开发环境**
- 快速展示所有可用摄像头
- 不需要固定的业务逻辑
- 便于测试和调试

#### 3. **个人或小型监控**
- 摄像头数量较少
- 用户希望自由选择监控内容
- 不需要复杂的配置管理

### 固定映射模式适用于：

#### 1. **专业监控中心**
- 每个监控画面有明确的业务含义
- 操作员需要快速定位特定设备
- 要求稳定和一致的显示

#### 2. **工业生产监控**
- 监控关键生产设备
- 每个画面对应特定的生产线或设备
- 需要长期稳定运行

#### 3. **安防监控系统**
- 重要区域的固定监控
- 需要快速响应特定位置的情况
- 要求配置的持久化和备份

## 🔧 实现细节对比

### 数据流程对比

#### 动态分配模式流程：
```
1. 加载树形数据
2. 遍历提取所有摄像头节点
3. 按顺序分配到监控画面
4. 用户交互时动态切换
5. 刷新页面重新分配
```

#### 固定映射模式流程：
```
1. 加载树形数据
2. 根据预定义配置查找对应摄像头
3. 显示固定的监控画面配置
4. 用户交互时更新配置
5. 刷新页面保持配置
```

### 代码复杂度对比

#### 动态分配模式：
- **算法复杂度**：O(n) - 需要遍历所有节点
- **状态管理**：复杂 - 需要处理动态分配逻辑
- **事件处理**：中等 - 需要处理切换和更新

#### 固定映射模式：
- **算法复杂度**：O(log n) - 精确查找特定节点
- **状态管理**：简单 - 基于固定配置
- **事件处理**：简单 - 基于配置更新

## 🚀 迁移指南

### 从动态分配迁移到固定映射

#### 1. **确定摄像头映射关系**
```typescript
// 分析现有摄像头节点，确定业务映射关系
const monitorScreenConfig = [
  { cameraId: 'camera-1-id', defaultTitle: '生产线A监控' },
  { cameraId: 'camera-2-id', defaultTitle: '质检区监控' },
  // ...
];
```

#### 2. **修改数据获取逻辑**
```typescript
// 从遍历提取改为精确查找
const findCameraNodeById = (nodes, targetId) => {
  // 实现精确查找逻辑
};
```

#### 3. **更新UI交互逻辑**
```typescript
// 修改点击事件，显示固定映射关系
const handleNodeClick = (data) => {
  const screenIndex = findScreenIndexByCameraId(data.id);
  // 显示对应的监控画面编号
};
```

### 从固定映射迁移到动态分配

#### 1. **移除固定配置**
```typescript
// 删除固定配置，改为动态提取
const extractAllCameraNodes = (nodes) => {
  // 实现遍历提取逻辑
};
```

#### 2. **实现自动分配逻辑**
```typescript
// 添加自动分配算法
const autoAssignCameras = (cameras) => {
  // 实现自动分配逻辑
};
```

#### 3. **增加用户配置功能**
```typescript
// 添加用户自定义配置
const userConfig = ref([]);
const saveUserConfig = () => {
  // 保存用户配置到本地存储
};
```

## 💾 固定映射模式完整代码备份

### 核心实现代码

#### 1. 数据结构定义
```typescript
// 固定的四个监控画面配置，每个画面对应一个特定的摄像头节点ID
const monitorScreenConfig = [
  { cameraId: '1-1-1-1-1', defaultTitle: '监控画面 1' }, // N-D2-5-1(纸箱打包机)
  { cameraId: '1-1-1-2-1', defaultTitle: '监控画面 2' }, // N-D2-5-2(包装生产线)
  { cameraId: '1-1-1-2-2', defaultTitle: '监控画面 3' }, // N-D2-5-3(质检设备)
  { cameraId: '1-1-2-1-1', defaultTitle: '监控画面 4' }, // N-D3-1-1(生产线A)
];

// 当前显示的四个监控画面数据
const currentMonitorScreens = ref<Array<{
  id: string;
  title: string;
  status: 'online' | 'offline' | 'connecting';
  cameraNode?: TreeNodeData | null;
}>>([]);
```

#### 2. 节点查找函数
```typescript
// 递归查找指定ID的摄像头节点
const findCameraNodeById = (nodes: TreeNodeData[], targetId: string): TreeNodeData | null => {
  for (const node of nodes) {
    if (node.id === targetId && node.type === 'camera') {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findCameraNodeById(node.children, targetId);
      if (found) return found;
    }
  }
  return null;
};

// 获取摄像头节点的完整路径
const getCameraNodePath = (nodes: TreeNodeData[], targetId: string, path: string[] = []): string[] => {
  for (const node of nodes) {
    const currentPath = [...path, node.label];

    if (node.id === targetId && node.type === 'camera') {
      return currentPath;
    }

    if (node.children && node.children.length > 0) {
      const found = getCameraNodePath(node.children, targetId, currentPath);
      if (found.length > 0) return found;
    }
  }
  return [];
};
```

#### 3. 监控画面更新逻辑
```typescript
// 更新监控画面数据
const updateMonitorScreens = () => {
  currentMonitorScreens.value = monitorScreenConfig.map((config, index) => {
    // 根据配置的摄像头ID查找对应的节点
    const cameraNode = findCameraNodeById(treeData.value, config.cameraId);

    if (cameraNode) {
      return {
        id: cameraNode.id,
        title: cameraNode.label,
        status: getRandomStatus(),
        cameraNode: cameraNode,
      };
    } else {
      return {
        id: `empty-${index}`,
        title: config.defaultTitle,
        status: 'offline' as const,
        cameraNode: null,
      };
    }
  });

  console.log('更新监控画面:', currentMonitorScreens.value);
};
```

#### 4. 树节点点击事件
```typescript
// 树节点点击事件
const handleNodeClick = (data: TreeNodeData) => {
  if (data.type === 'camera') {
    console.log('选中摄像头:', data.label);

    // 查找该摄像头对应的监控画面
    const screenIndex = currentMonitorScreens.value.findIndex(screen => screen.id === data.id);
    if (screenIndex !== -1) {
      ElMessage.success(`摄像头 ${data.label} 对应监控画面 ${screenIndex + 1}`);
    } else {
      ElMessage.info(`摄像头 ${data.label} 暂未分配到监控画面`);
    }
  }
};
```

#### 5. 摄像头切换逻辑
```typescript
// 切换摄像头到指定监控画面
const switchCameraToScreen = (camera: TreeNodeData, screenIndex: number) => {
  if (screenIndex >= 0 && screenIndex < currentMonitorScreens.value.length) {
    // 更新配置
    monitorScreenConfig[screenIndex].cameraId = camera.id;

    // 更新当前显示的监控画面数据
    currentMonitorScreens.value[screenIndex] = {
      id: camera.id,
      title: camera.label,
      status: getRandomStatus(),
      cameraNode: camera,
    };

    ElMessage.info(`${camera.label} 已切换到监控画面 ${screenIndex + 1}`);
  }
};
```

#### 6. 监控画面事件处理
```typescript
// 监控画面事件处理
const handleMonitorClick = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('点击监控画面:', title, screen);

  if (!screen.cameraNode) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头`);
  } else {
    ElMessage.info(`当前监控画面: ${title} (${screen.cameraNode.label})`);
  }
};

const handleMonitorFullscreen = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('全屏监控画面:', title, screen);

  if (!screen.cameraNode) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法全屏`);
  } else {
    ElMessage.success(`${screen.cameraNode.label} 进入全屏模式`);
  }
};

const handleMonitorRecord = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('录制监控画面:', title, screen);

  if (!screen.cameraNode) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法录制`);
  } else {
    ElMessage.success(`开始录制 ${screen.cameraNode.label}`);
  }
};

const handleMonitorScreenshot = (title: string, screenIndex: number) => {
  const screen = currentMonitorScreens.value[screenIndex];
  console.log('截图监控画面:', title, screen);

  if (!screen.cameraNode) {
    ElMessage.warning(`监控画面 ${screenIndex + 1} 暂无摄像头，无法截图`);
  } else {
    ElMessage.success(`${screen.cameraNode.label} 截图成功`);
  }
};
```

#### 7. 模板部分关键代码
```vue
<!-- 顶部工具栏 -->
<div class="bg-card border-border mb-2 rounded-[var(--radius)] border p-3">
  <div class="flex items-center justify-between">
    <div>
      <h2 class="text-foreground text-lg font-semibold">实时监控</h2>
      <p class="text-muted-foreground text-xs mt-1">
        每个监控画面对应一个固定的摄像头节点，右键点击摄像头可切换画面
      </p>
    </div>
    <!-- 工具按钮 -->
  </div>
</div>

<!-- 监控画面网格 -->
<div class="grid h-full grid-cols-2 gap-3">
  <MonitorScreen
    v-for="(screen, index) in currentMonitorScreens"
    :key="screen.id"
    :title="screen.title"
    :status="screen.status"
    :placeholder="!screen.cameraNode ? '暂无摄像头' : '暂无信号'"
    @click="(title) => handleMonitorClick(title, index)"
    @fullscreen="(title) => handleMonitorFullscreen(title, index)"
    @record="(title) => handleMonitorRecord(title, index)"
    @screenshot="(title) => handleMonitorScreenshot(title, index)"
  />
</div>
```

### 配置文件示例

#### 监控画面配置管理
```typescript
// 可以将配置提取到单独的配置文件中
// config/monitor-screen-config.ts
export interface MonitorScreenConfig {
  cameraId: string;
  defaultTitle: string;
  description?: string;
  priority?: number;
}

export const MONITOR_SCREEN_CONFIGS: MonitorScreenConfig[] = [
  {
    cameraId: '1-1-1-1-1',
    defaultTitle: '纸箱打包机监控',
    description: '南配电房纸箱打包机设备监控',
    priority: 1,
  },
  {
    cameraId: '1-1-1-2-1',
    defaultTitle: '包装生产线监控',
    description: '北配电房包装生产线设备监控',
    priority: 2,
  },
  {
    cameraId: '1-1-1-2-2',
    defaultTitle: '质检设备监控',
    description: '北配电房质检设备监控',
    priority: 3,
  },
  {
    cameraId: '1-1-2-1-1',
    defaultTitle: '生产线A监控',
    description: '生产车间生产线A区域监控',
    priority: 4,
  },
];

// 配置持久化功能
export const saveMonitorConfig = (config: MonitorScreenConfig[]) => {
  localStorage.setItem('monitor-screen-config', JSON.stringify(config));
};

export const loadMonitorConfig = (): MonitorScreenConfig[] => {
  const saved = localStorage.getItem('monitor-screen-config');
  return saved ? JSON.parse(saved) : MONITOR_SCREEN_CONFIGS;
};
```

### 使用说明

#### 1. 初始化配置
```typescript
// 在组件初始化时加载配置
onMounted(() => {
  // 加载保存的配置或使用默认配置
  const savedConfig = loadMonitorConfig();
  monitorScreenConfig.splice(0, monitorScreenConfig.length, ...savedConfig);

  // 加载树形数据
  loadTreeData();
});
```

#### 2. 配置更新
```typescript
// 当用户切换摄像头时，自动保存配置
const switchCameraToScreen = (camera: TreeNodeData, screenIndex: number) => {
  // ... 切换逻辑

  // 保存配置
  saveMonitorConfig(monitorScreenConfig);
};
```

#### 3. 配置重置
```typescript
// 提供配置重置功能
const resetMonitorConfig = () => {
  monitorScreenConfig.splice(0, monitorScreenConfig.length, ...MONITOR_SCREEN_CONFIGS);
  updateMonitorScreens();
  saveMonitorConfig(monitorScreenConfig);
  ElMessage.success('监控画面配置已重置');
};
```

## 🎉 总结

两种实现方式各有优劣，选择哪种方式取决于具体的业务需求：

### 选择动态分配模式，如果：
- ✅ 需要高度的灵活性和可配置性
- ✅ 摄像头数量和类型经常变化
- ✅ 用户需要自由组合监控画面
- ✅ 系统主要用于演示或开发

### 选择固定映射模式，如果：
- ✅ 需要稳定和一致的监控画面
- ✅ 每个画面有明确的业务含义
- ✅ 要求快速定位和响应
- ✅ 系统用于生产环境

### 实现切换建议

如果需要从动态分配模式切换到固定映射模式，可以按照以下步骤：

1. **备份当前实现**：保存动态分配模式的代码
2. **替换数据结构**：使用上述备份代码中的数据结构
3. **更新算法逻辑**：替换为精确查找的实现
4. **修改交互逻辑**：更新事件处理函数
5. **调整UI界面**：更新模板和样式
6. **测试验证**：确保所有功能正常工作

在实际项目中，也可以考虑**混合模式**：提供固定映射作为默认配置，同时允许用户进行动态调整，并支持配置的保存和恢复。这样既保证了稳定性，又提供了灵活性。

**备份代码已完整保存在此文档中，可随时参考和使用。**

## 🔄 快速切换指南

### 从动态分配模式切换到固定映射模式

#### 步骤1：替换数据结构
```typescript
// 在 apps/web-ele/src/views/camera/realtime-video/index.vue 中
// 找到并替换以下代码：

// 替换前（动态分配模式）
const currentMonitorScreens = ref<Array<{
  id: string;
  title: string;
  status: 'online' | 'offline' | 'connecting';
}>>([]);

// 替换后（固定映射模式）
const monitorScreenConfig = [
  { cameraId: '1-1-1-1-1', defaultTitle: '监控画面 1' },
  { cameraId: '1-1-1-2-1', defaultTitle: '监控画面 2' },
  { cameraId: '1-1-1-2-2', defaultTitle: '监控画面 3' },
  { cameraId: '1-1-2-1-1', defaultTitle: '监控画面 4' },
];

const currentMonitorScreens = ref<Array<{
  id: string;
  title: string;
  status: 'online' | 'offline' | 'connecting';
  cameraNode?: TreeNodeData | null;
}>>([]);
```

#### 步骤2：替换算法函数
```typescript
// 替换前（动态分配模式）
const extractCameraNodes = (nodes: TreeNodeData[]): TreeNodeData[] => {
  // ... 动态提取逻辑
};

// 替换后（固定映射模式）
const findCameraNodeById = (nodes: TreeNodeData[], targetId: string): TreeNodeData | null => {
  // ... 精确查找逻辑（参考上面备份代码）
};
```

#### 步骤3：替换更新逻辑
```typescript
// 替换前（动态分配模式）
const updateMonitorScreens = () => {
  const cameraNodes = extractCameraNodes(treeData.value);
  // ... 动态分配逻辑
};

// 替换后（固定映射模式）
const updateMonitorScreens = () => {
  currentMonitorScreens.value = monitorScreenConfig.map((config, index) => {
    // ... 固定映射逻辑（参考上面备份代码）
  });
};
```

#### 步骤4：替换事件处理
```typescript
// 替换所有事件处理函数中的判断逻辑：
// 从 screen.id.startsWith('empty-') 改为 !screen.cameraNode
```

#### 步骤5：更新模板
```vue
<!-- 替换占位符判断 -->
<!-- 从 -->
:placeholder="screen.id.startsWith('empty-') ? '暂无摄像头' : '暂无信号'"

<!-- 改为 -->
:placeholder="!screen.cameraNode ? '暂无摄像头' : '暂无信号'"
```

### 一键切换脚本

为了方便切换，可以创建一个脚本文件：

#### `scripts/switch-to-fixed-mapping.js`
```javascript
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '../apps/web-ele/src/views/camera/realtime-video/index.vue');

// 读取当前文件
let content = fs.readFileSync(filePath, 'utf8');

// 执行替换操作
const replacements = [
  // 数据结构替换
  {
    from: /\/\/ 当前显示的四个监控画面[\s\S]*?\]\>\>\(\[\]\);/,
    to: `// 固定的四个监控画面配置
const monitorScreenConfig = [
  { cameraId: '1-1-1-1-1', defaultTitle: '监控画面 1' },
  { cameraId: '1-1-1-2-1', defaultTitle: '监控画面 2' },
  { cameraId: '1-1-1-2-2', defaultTitle: '监控画面 3' },
  { cameraId: '1-1-2-1-1', defaultTitle: '监控画面 4' },
];

const currentMonitorScreens = ref<Array<{
  id: string;
  title: string;
  status: 'online' | 'offline' | 'connecting';
  cameraNode?: TreeNodeData | null;
}>>([]);`
  },
  // 更多替换规则...
];

// 执行替换
replacements.forEach(({ from, to }) => {
  content = content.replace(from, to);
});

// 写回文件
fs.writeFileSync(filePath, content, 'utf8');
console.log('✅ 已切换到固定映射模式');
```

### 使用说明

1. **手动切换**：按照上述步骤逐步替换代码
2. **脚本切换**：运行 `node scripts/switch-to-fixed-mapping.js`
3. **验证功能**：启动开发服务器测试所有功能
4. **回滚方案**：如需回滚，可以使用 Git 或参考动态分配模式的代码

### 注意事项

- ⚠️ **备份代码**：切换前请确保代码已提交到 Git
- ⚠️ **测试验证**：切换后务必测试所有功能
- ⚠️ **配置调整**：根据实际的摄像头节点ID调整 `monitorScreenConfig`
- ⚠️ **UI适配**：检查所有UI交互是否正常工作

通过这个详细的备份和切换指南，您可以随时在两种模式之间进行切换，选择最适合当前业务需求的实现方式。
